#!/usr/bin/env python3
import requests
import json

def test_permission_debug():
    base_url = 'http://localhost:5000'
    
    # 登录获取token
    login_response = requests.post(f'{base_url}/api/auth/login', json={
        'username': 'admin',
        'password': 'admin123'
    })
    
    if login_response.status_code != 200:
        print(f"登录失败: {login_response.status_code}")
        return
    
    token = login_response.json()['access_token']
    headers = {'Authorization': f'Bearer {token}'}
    
    print("=== 测试工单API权限装饰器 ===")
    response = requests.get(f'{base_url}/api/work-orders', headers=headers)
    print(f"工单API响应状态码: {response.status_code}")
    
    if response.status_code == 200:
        print("权限检查通过，API调用成功")
    else:
        print(f"权限检查失败: {response.text}")

if __name__ == '__main__':
    test_permission_debug()