#!/usr/bin/env python3
import sys
sys.path.append('/home/<USER>/dispatch-system/backend')

from wsgi import create_app
from app.models.user import User
from app.models.work_order import WorkOrder
from app.models import db
from sqlalchemy import text

app = create_app()
app.app_context().push()

print('=== 检查工单4的详细信息 ===')
work_order = WorkOrder.query.get(4)
if work_order:
    print(f'工单ID: {work_order.id}')
    print(f'工单号: {work_order.order_number}')
    print(f'标题: {work_order.title}')
    print(f'状态: {work_order.status}')
    print(f'创建者ID: {work_order.creator_id}')
    print(f'分配者ID: {work_order.assignee_id}')
    print(f'区域ID: {work_order.area_id}')
    print(f'组ID: {work_order.group_id}')
    
    # 获取创建者信息
    if work_order.creator:
        print(f'创建者: {work_order.creator.username} (角色: {work_order.creator.role})')
    
    # 获取分配者信息
    if work_order.assignee:
        print(f'分配者: {work_order.assignee.username} (角色: {work_order.assignee.role})')
else:
    print('工单4不存在')

print('\n=== 检查维护人员用户信息 ===')
maintenance_users = User.query.filter(User.role.in_(['manager', 'user'])).all()
for user in maintenance_users:
    print(f'用户: {user.username} (ID: {user.id}, 角色: {user.role}, 区域ID: {user.area_id}, 组ID: {user.group_id})')

print('\n=== 检查最近的API访问日志 ===')
# 查看最近的日志记录
import os
log_file = '/home/<USER>/dispatch-system/backend/backend.log'
if os.path.exists(log_file):
    with open(log_file, 'r') as f:
        lines = f.readlines()
        # 获取最后50行日志
        recent_lines = lines[-50:] if len(lines) > 50 else lines
        for line in recent_lines:
            if 'work-orders/4' in line or 'process-records' in line:
                print(line.strip())