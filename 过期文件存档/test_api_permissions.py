#!/usr/bin/env python3
import requests
import json

# 测试API权限问题
base_url = 'http://localhost:5000/api'

def test_login():
    """测试登录"""
    login_data = {
        'username': 'admin',
        'password': 'admin123'
    }
    
    response = requests.post(f'{base_url}/auth/login', json=login_data)
    print(f"登录响应状态码: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"登录成功: {data.get('message')}")
        return data.get('access_token')
    else:
        print(f"登录失败: {response.text}")
        return None

def test_work_orders_api(token):
    """测试工单API"""
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    print("\n=== 测试工单API ===")
    response = requests.get(f'{base_url}/work-orders', headers=headers)
    print(f"工单API响应状态码: {response.status_code}")
    print(f"工单API响应内容: {response.text[:500]}")
    
    return response.status_code == 200

def test_materials_api(token):
    """测试材料API"""
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    print("\n=== 测试材料API ===")
    response = requests.get(f'{base_url}/materials', headers=headers)
    print(f"材料API响应状态码: {response.status_code}")
    print(f"材料API响应内容: {response.text[:500]}")
    
    return response.status_code == 200

def test_users_api(token):
    """测试用户API"""
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    print("\n=== 测试用户API ===")
    response = requests.get(f'{base_url}/users', headers=headers)
    print(f"用户API响应状态码: {response.status_code}")
    print(f"用户API响应内容: {response.text[:500]}")
    
    return response.status_code == 200

def main():
    print("开始测试API权限问题...")
    
    # 登录获取token
    token = test_login()
    if not token:
        print("登录失败，无法继续测试")
        return
    
    # 测试各个API
    test_work_orders_api(token)
    test_materials_api(token)
    test_users_api(token)
    
    print("\n测试完成")

if __name__ == '__main__':
    main()