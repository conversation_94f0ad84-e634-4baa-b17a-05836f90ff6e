# 前端页面树形结构说明

## 概述
本文档详细描述了调度系统前端的页面结构、组织方式和功能模块划分。系统采用 Vue 3 + TypeScript + Element Plus 技术栈，支持桌面端和移动端双端适配。

## 项目结构树

```
frontend/src/
├── App.vue                          # 根组件
├── main.ts                          # 应用入口文件
├── api/                             # API接口层
│   ├── axios.ts                     # HTTP客户端配置
│   ├── index.ts                     # API统一导出
│   ├── materialUsage.ts             # 材料使用API
│   ├── materials.ts                 # 材料管理API
│   └── workOrders.ts                # 工单管理API
├── assets/                          # 静态资源
│   ├── base.css                     # 基础样式
│   ├── logo.svg                     # 系统Logo
│   └── main.css                     # 主样式文件
├── components/                      # 组件库
│   ├── HelloWorld.vue               # 示例组件
│   ├── MainLayout.vue               # 主布局组件
│   ├── TheWelcome.vue               # 欢迎组件
│   ├── WelcomeItem.vue              # 欢迎项组件
│   ├── __tests__/                   # 组件测试
│   │   └── HelloWorld.spec.ts
│   ├── icons/                       # 图标组件
│   │   ├── IconCommunity.vue
│   │   ├── IconDocumentation.vue
│   │   ├── IconEcosystem.vue
│   │   ├── IconSupport.vue
│   │   └── IconTooling.vue
│   └── mobile/                      # 移动端组件
│       ├── CompleteDialog.vue       # 完成对话框
│       ├── FeedbackDialog.vue       # 反馈对话框
│       ├── MobileHeader.vue         # 移动端头部
│       ├── StatsCards.vue           # 统计卡片
│       ├── TabBar.vue               # 底部标签栏
│       └── WorkOrderList.vue        # 工单列表组件
├── composables/                     # 组合式函数
│   ├── useMaterials.ts              # 材料管理逻辑
│   ├── useWorkOrderActions.ts       # 工单操作逻辑
│   └── useWorkOrders.ts             # 工单管理逻辑
├── router/                          # 路由配置
│   └── index.ts                     # 路由定义和守卫
├── stores/                          # 状态管理
│   ├── auth.ts                      # 认证状态
│   └── workOrders.ts                # 工单状态
├── utils/                           # 工具函数
│   └── device.ts                    # 设备检测工具
└── views/                           # 页面视图
    ├── AboutView.vue                # 关于页面
    ├── AreaManagementView.vue       # 区域管理
    ├── DashboardView.vue            # 仪表板
    ├── HomeView.vue                 # 首页
    ├── InventoryMaterialsView.vue   # 材料库存管理
    ├── InventoryUsageView.vue       # 材料使用记录
    ├── LoginView.vue                # 登录页面
    ├── MobileLoginView.vue          # 移动端登录
    ├── MobileMaintenanceView.vue    # 移动端维护
    ├── MobileWorkOrderDetailView.vue # 移动端工单详情
    ├── MobileWorkOrderEditView.vue  # 移动端工单编辑
    ├── StyleTestView.vue            # 样式测试页面
    ├── UserManagementView.vue       # 用户管理
    ├── WorkOrderCompletedView.vue   # 已完成工单
    ├── WorkOrderConfigView.vue      # 工单配置
    ├── WorkOrderCreateView.vue      # 创建工单
    ├── WorkOrderDetailView.vue      # 工单详情
    ├── WorkOrderEditView.vue        # 编辑工单
    ├── WorkOrderPendingView.vue     # 待处理工单
    ├── WorkOrderReportsView.vue     # 工单报表
    ├── WorkOrdersView.vue           # 工单列表
    └── workorderdetailview.vue      # 工单详情（重复文件）
```

## 功能模块划分

### 1. 认证模块
- **LoginView.vue** - 桌面端登录页面
- **MobileLoginView.vue** - 移动端登录页面
- **stores/auth.ts** - 认证状态管理

### 2. 仪表板模块
- **DashboardView.vue** - 系统仪表板，显示关键指标和统计信息
- **HomeView.vue** - 系统首页
- **AboutView.vue** - 关于页面

### 3. 工单管理模块
#### 3.1 桌面端工单页面
- **WorkOrdersView.vue** - 工单列表页面
- **WorkOrderCreateView.vue** - 创建工单页面
- **WorkOrderDetailView.vue** - 工单详情页面
- **WorkOrderEditView.vue** - 编辑工单页面
- **WorkOrderPendingView.vue** - 待处理工单页面
- **WorkOrderCompletedView.vue** - 已完成工单页面
- **WorkOrderReportsView.vue** - 工单报表页面
- **WorkOrderConfigView.vue** - 工单配置页面

#### 3.2 移动端工单页面
- **MobileMaintenanceView.vue** - 移动端维护主页
- **MobileWorkOrderDetailView.vue** - 移动端工单详情
- **MobileWorkOrderEditView.vue** - 移动端工单编辑

#### 3.3 工单相关组件
- **components/mobile/WorkOrderList.vue** - 工单列表组件
- **components/mobile/CompleteDialog.vue** - 完成对话框
- **components/mobile/FeedbackDialog.vue** - 反馈对话框
- **composables/useWorkOrders.ts** - 工单管理逻辑
- **composables/useWorkOrderActions.ts** - 工单操作逻辑
- **stores/workOrders.ts** - 工单状态管理

### 4. 库存管理模块
- **InventoryMaterialsView.vue** - 材料库存管理页面
- **InventoryUsageView.vue** - 材料使用记录页面
- **composables/useMaterials.ts** - 材料管理逻辑
- **api/materials.ts** - 材料管理API
- **api/materialUsage.ts** - 材料使用API

### 5. 系统管理模块
- **UserManagementView.vue** - 用户管理页面
- **AreaManagementView.vue** - 区域管理页面

### 6. 移动端专用组件
- **components/mobile/MobileHeader.vue** - 移动端头部组件
- **components/mobile/TabBar.vue** - 底部标签栏
- **components/mobile/StatsCards.vue** - 统计卡片组件

### 7. 布局和导航
- **components/MainLayout.vue** - 主布局组件，包含侧边栏、顶部导航和面包屑
- **router/index.ts** - 路由配置，包含权限控制和设备适配

### 8. 工具和配置
- **utils/device.ts** - 设备检测工具
- **api/axios.ts** - HTTP客户端配置
- **StyleTestView.vue** - 样式测试页面（开发环境）

## 路由结构

### 桌面端路由
```
/
├── /login                    # 登录页面
├── /dashboard                # 仪表板
├── /work-orders             # 工单管理
│   ├── /create             # 创建工单
│   ├── /pending            # 待处理工单
│   ├── /completed          # 已完成工单
│   ├── /reports            # 工单报表
│   ├── /:id                # 工单详情
│   └── /:id/edit           # 编辑工单
├── /inventory               # 库存管理
│   ├── /materials          # 材料管理
│   └── /usage              # 使用记录
├── /users                   # 用户管理（管理员）
├── /areas                   # 区域管理（管理员）
├── /work-order-config       # 工单配置
└── /style-test             # 样式测试（开发环境）
```

### 移动端路由
```
/mobile/
├── /login                   # 移动端登录
├── /maintenance             # 维护主页
├── /work-orders/:id         # 工单详情
└── /work-orders/:id/edit    # 工单编辑
```

## 权限控制

### 角色权限
- **admin** - 管理员，拥有所有权限
- **manager** - 经理，拥有工单配置权限
- **user** - 普通用户，基础工单操作权限

### 页面权限
- 所有页面都需要登录认证（除登录页面）
- 用户管理和区域管理仅管理员可访问
- 工单配置管理员和经理可访问

## 设备适配

### 响应式设计
- 桌面端：使用 Element Plus 组件库，支持大屏幕操作
- 移动端：专门的移动端页面和组件，优化触摸操作
- 自动设备检测：根据 User-Agent 自动跳转到对应端

### 移动端特性
- 简化的导航结构
- 触摸友好的操作界面
- 专用的移动端组件
- 优化的表单和列表展示

## 状态管理

### Pinia Stores
- **auth.ts** - 用户认证状态、权限管理
- **workOrders.ts** - 工单数据缓存、状态管理

### Composables
- **useWorkOrders.ts** - 工单CRUD操作
- **useWorkOrderActions.ts** - 工单状态变更操作
- **useMaterials.ts** - 材料管理操作

## API 接口层

### 接口模块
- **workOrders.ts** - 工单相关API
- **materials.ts** - 材料管理API
- **materialUsage.ts** - 材料使用API
- **axios.ts** - HTTP客户端配置，包含拦截器

## 组件复用策略

### 通用组件
- **MainLayout.vue** - 主布局，所有桌面端页面共用
- **components/icons/** - 图标组件库

### 移动端组件
- **mobile/** 目录下的组件专为移动端设计
- 独立的样式和交互逻辑

### 表单组件
- 各页面的表单组件相对独立
- 共享验证规则和样式

## 开发和测试

### 开发工具
- **StyleTestView.vue** - 样式测试页面，仅开发环境可见
- **__tests__/** - 组件单元测试

### 构建配置
- TypeScript 支持
- Vite 构建工具
- ESLint 代码规范
- Prettier 代码格式化

## 总结

该前端架构具有以下特点：
1. **模块化设计** - 按功能模块组织代码
2. **双端适配** - 桌面端和移动端分离设计
3. **权限控制** - 基于角色的访问控制
4. **状态管理** - 使用 Pinia 进行状态管理
5. **组件复用** - 合理的组件拆分和复用
6. **类型安全** - TypeScript 提供类型检查
7. **响应式设计** - 适配不同屏幕尺寸

这种架构设计有利于代码维护、功能扩展和团队协作开发。