#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from wsgi import create_app
from app.models import db
from app.models.user import User
from app.models.group import Group
from datetime import datetime

def create_test_users():
    app = create_app()
    with app.app_context():
        # 创建默认组
        if not Group.query.first():
            print("创建默认组...")
            group1 = Group(name='维修一组', description='负责A区域的维修任务')
            group2 = Group(name='维修二组', description='负责B区域的维修任务')
            db.session.add_all([group1, group2])
            db.session.commit()
        
        group1 = Group.query.filter_by(name='维修一组').first()
        
        # 创建管理员用户
        if not User.query.filter_by(username='admin').first():
            print("创建管理员用户...")
            admin = User(
                username='admin',
                email='<EMAIL>',
                role='admin',
                name='系统管理员',
                employee_id='A001',
                phone='13800000000',
                position='系统管理员',
                department='信息部',
                area='总部',
                id_card='110101199001011234',
                ethnicity='汉族',
                gender='男',
                hire_date=datetime.now().date(),
                is_driver=False,
                group_id=group1.id if group1 else None
            )
            admin.set_password('admin123')
            db.session.add(admin)
            db.session.commit()
            print(f"管理员用户创建成功: {admin.username}")
        
        # 创建经理用户
        if not User.query.filter_by(username='manager').first():
            print("创建经理用户...")
            manager = User(
                username='manager',
                email='<EMAIL>',
                role='manager',
                name='王经理',
                employee_id='M001',
                phone='13800000001',
                position='项目经理',
                department='维修部',
                area='A区',
                id_card='110101199001011235',
                ethnicity='汉族',
                gender='男',
                hire_date=datetime.now().date(),
                is_driver=False,
                group_id=group1.id if group1 else None
            )
            manager.set_password('manager123')
            db.session.add(manager)
            db.session.commit()
            print(f"经理用户创建成功: {manager.username}")
        
        # 创建普通用户
        if not User.query.filter_by(username='user').first():
            print("创建普通用户...")
            user = User(
                username='user',
                email='<EMAIL>',
                role='user',
                name='张三',
                employee_id='U001',
                phone='13800000002',
                position='工程师',
                department='维修部',
                area='A区',
                id_card='110101199001011236',
                ethnicity='汉族',
                gender='男',
                hire_date=datetime.now().date(),
                is_driver=False,
                group_id=group1.id if group1 else None
            )
            user.set_password('user123')
            db.session.add(user)
            db.session.commit()
            print(f"普通用户创建成功: {user.username}")
        
        # 验证创建的用户
        users = User.query.all()
        print(f"\n当前数据库中的用户:")
        for u in users:
            print(f"- 用户名: {u.username}, 姓名: {u.name}, 角色: {u.role}")

if __name__ == '__main__':
    create_test_users()