#!/usr/bin/env python3
import requests
import json

# 配置
BASE_URL = 'http://localhost:5000/api'
WORK_ORDER_ID = 4

def test_login_and_work_order_access():
    """测试登录和工单访问"""
    session = requests.Session()
    
    print('=== 测试维护人员登录 ===')
    # 尝试不同的维护人员账号
    test_users = [
        {'username': '18009981028', 'password': '123456'},
        {'username': '18109988155', 'password': '123456'},
        {'username': '18109984459', 'password': '123456'},
        {'username': 'admin', 'password': 'admin123'}
    ]
    
    for user_creds in test_users:
        print(f"\n--- 测试用户: {user_creds['username']} ---")
        
        # 1. 登录
        try:
            login_response = session.post(
                f'{BASE_URL}/auth/login',
                json=user_creds,
                headers={'Content-Type': 'application/json'}
            )
            
            print(f'登录状态码: {login_response.status_code}')
            
            if login_response.status_code == 200:
                login_data = login_response.json()
                token = login_data.get('access_token')
                user_info = login_data.get('user', {})
                
                print(f'登录成功: {user_info.get("name", "未知")} (角色: {user_info.get("role", "未知")})')
                print(f'Token: {token[:50] if token else "无"}...')
                
                if token:
                    # 2. 测试获取工单详情
                    headers = {
                        'Authorization': f'Bearer {token}',
                        'Content-Type': 'application/json'
                    }
                    
                    print(f'\n--- 测试工单API ---')
                    
                    # 测试获取工单详情
                    try:
                        work_order_response = session.get(
                            f'{BASE_URL}/work-orders/{WORK_ORDER_ID}',
                            headers=headers
                        )
                        print(f'工单详情状态码: {work_order_response.status_code}')
                        
                        if work_order_response.status_code == 200:
                            work_order_data = work_order_response.json()
                            print(f'工单标题: {work_order_data.get("title", "未知")}')
                            print(f'工单状态: {work_order_data.get("status", "未知")}')
                        else:
                            print(f'获取工单失败: {work_order_response.text}')
                            
                    except Exception as e:
                        print(f'工单API请求异常: {e}')
                    
                    # 测试获取处理记录
                    try:
                        process_records_response = session.get(
                            f'{BASE_URL}/work-orders/{WORK_ORDER_ID}/process-records',
                            headers=headers
                        )
                        print(f'处理记录状态码: {process_records_response.status_code}')
                        
                        if process_records_response.status_code == 200:
                            records_data = process_records_response.json()
                            print(f'处理记录数量: {len(records_data.get("items", []))}')
                        else:
                            print(f'获取处理记录失败: {process_records_response.text}')
                            
                    except Exception as e:
                        print(f'处理记录API请求异常: {e}')
                        
            else:
                print(f'登录失败: {login_response.text}')
                
        except Exception as e:
            print(f'登录请求异常: {e}')

if __name__ == '__main__':
    test_login_and_work_order_access()