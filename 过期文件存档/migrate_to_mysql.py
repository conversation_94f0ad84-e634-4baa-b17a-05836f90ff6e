#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库迁移脚本：从SQLite迁移到MySQL
"""

import os
import sys
import sqlite3
import pymysql
from datetime import datetime
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def get_sqlite_connection():
    """获取SQLite数据库连接"""
    db_path = os.path.join(os.path.dirname(__file__), 'instance', 'dispatch_system.db')
    if not os.path.exists(db_path):
        print(f"❌ SQLite数据库文件不存在: {db_path}")
        return None
    return sqlite3.connect(db_path)

def get_mysql_connection():
    """获取MySQL数据库连接"""
    try:
        connection = pymysql.connect(
            host='************',
            port=3306,
            user='dispatch',
            password='csERiZzeMWe4Z2HC',
            database='dispatch_system',
            charset='utf8mb4',
            autocommit=False
        )
        return connection
    except Exception as e:
        print(f"❌ 连接MySQL数据库失败: {e}")
        return None

def get_table_list(sqlite_conn):
    """获取SQLite中的所有表"""
    cursor = sqlite_conn.cursor()
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
    tables = [row[0] for row in cursor.fetchall()]
    return tables

def get_table_schema(sqlite_conn, table_name):
    """获取表结构"""
    cursor = sqlite_conn.cursor()
    cursor.execute(f"PRAGMA table_info({table_name})")
    columns = cursor.fetchall()
    return columns

def convert_sqlite_type_to_mysql(sqlite_type):
    """将SQLite数据类型转换为MySQL数据类型"""
    sqlite_type = sqlite_type.upper()
    
    type_mapping = {
        'INTEGER': 'INT',
        'TEXT': 'TEXT',
        'REAL': 'DECIMAL(10,2)',
        'BLOB': 'BLOB',
        'BOOLEAN': 'TINYINT(1)',
        'DATETIME': 'DATETIME',
        'DATE': 'DATE',
        'VARCHAR': 'VARCHAR',
        'CHAR': 'CHAR'
    }
    
    # 处理带长度的类型
    for sqlite_key, mysql_type in type_mapping.items():
        if sqlite_type.startswith(sqlite_key):
            if '(' in sqlite_type and sqlite_key in ['VARCHAR', 'CHAR']:
                return sqlite_type.replace(sqlite_key, mysql_type.split('(')[0])
            return mysql_type
    
    # 默认返回TEXT
    return 'TEXT'

def create_mysql_table(mysql_conn, table_name, columns):
    """在MySQL中创建表"""
    cursor = mysql_conn.cursor()
    
    # 构建CREATE TABLE语句
    column_definitions = []
    for col in columns:
        cid, name, col_type, notnull, default_value, pk = col
        mysql_type = convert_sqlite_type_to_mysql(col_type)
        
        col_def = f"`{name}` {mysql_type}"
        
        if notnull:
            col_def += " NOT NULL"
        
        if default_value is not None:
            if mysql_type in ['TEXT', 'VARCHAR', 'CHAR'] and default_value != 'NULL':
                col_def += f" DEFAULT '{default_value}'"
            elif default_value != 'NULL':
                col_def += f" DEFAULT {default_value}"
        
        if pk:
            col_def += " PRIMARY KEY"
            if mysql_type == 'INT':
                col_def += " AUTO_INCREMENT"
        
        column_definitions.append(col_def)
    
    column_defs_str = ',\n  '.join(column_definitions)
    create_sql = f"CREATE TABLE IF NOT EXISTS `{table_name}` (\n  {column_defs_str}\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci"
    
    try:
        cursor.execute(f"DROP TABLE IF EXISTS `{table_name}`")
        cursor.execute(create_sql)
        print(f"✅ 创建表 {table_name} 成功")
        return True
    except Exception as e:
        print(f"❌ 创建表 {table_name} 失败: {e}")
        print(f"SQL: {create_sql}")
        import traceback
        traceback.print_exc()
        return False

def migrate_table_data(sqlite_conn, mysql_conn, table_name):
    """迁移表数据"""
    sqlite_cursor = sqlite_conn.cursor()
    mysql_cursor = mysql_conn.cursor()
    
    try:
        # 获取SQLite表数据
        sqlite_cursor.execute(f"SELECT * FROM {table_name}")
        rows = sqlite_cursor.fetchall()
        
        if not rows:
            print(f"📝 表 {table_name} 无数据")
            return True
        
        # 获取列名
        sqlite_cursor.execute(f"PRAGMA table_info({table_name})")
        columns = [col[1] for col in sqlite_cursor.fetchall()]
        
        # 获取MySQL表结构以了解字段类型
        mysql_cursor.execute(f"DESCRIBE `{table_name}`")
        mysql_columns = {row[0]: row[1] for row in mysql_cursor.fetchall()}
        
        # 处理数据，转换空字符串为NULL（对于数值类型字段）
        processed_rows = []
        for row in rows:
            processed_row = []
            for i, value in enumerate(row):
                column_name = columns[i]
                column_type = mysql_columns.get(column_name, '').lower()
                
                # 如果是数值类型字段且值为空字符串，转换为None
                if value == '' and any(t in column_type for t in ['int', 'decimal', 'float', 'double']):
                    processed_row.append(None)
                else:
                    processed_row.append(value)
            processed_rows.append(tuple(processed_row))
        
        # 构建INSERT语句
        placeholders = ', '.join(['%s'] * len(columns))
        column_names = ', '.join([f'`{col}`' for col in columns])
        insert_sql = f"INSERT INTO `{table_name}` ({column_names}) VALUES ({placeholders})"
        
        # 批量插入数据
        mysql_cursor.executemany(insert_sql, processed_rows)
        mysql_conn.commit()
        
        print(f"✅ 迁移表 {table_name} 数据成功，共 {len(rows)} 条记录")
        return True
        
    except Exception as e:
        print(f"❌ 迁移表 {table_name} 数据失败: {e}")
        import traceback
        traceback.print_exc()
        mysql_conn.rollback()
        return False

def main():
    """主函数"""
    print("🚀 开始数据库迁移：SQLite → MySQL")
    print(f"迁移时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 连接数据库
    sqlite_conn = get_sqlite_connection()
    if not sqlite_conn:
        return False
    
    mysql_conn = get_mysql_connection()
    if not mysql_conn:
        sqlite_conn.close()
        return False
    
    try:
        # 获取所有表
        tables = get_table_list(sqlite_conn)
        print(f"\n📋 发现 {len(tables)} 个表需要迁移:")
        for table in tables:
            print(f"  - {table}")
        
        success_count = 0
        failed_tables = []
        
        # 逐个迁移表
        for table_name in tables:
            print(f"\n{'='*50}")
            print(f"正在迁移表: {table_name}")
            print(f"{'='*50}")
            
            # 获取表结构
            columns = get_table_schema(sqlite_conn, table_name)
            
            # 创建MySQL表
            if create_mysql_table(mysql_conn, table_name, columns):
                # 迁移数据
                if migrate_table_data(sqlite_conn, mysql_conn, table_name):
                    success_count += 1
                else:
                    failed_tables.append(table_name)
            else:
                failed_tables.append(table_name)
        
        # 输出迁移结果
        print(f"\n{'='*60}")
        print("🎯 数据库迁移完成")
        print(f"{'='*60}")
        print(f"总计表数: {len(tables)}")
        print(f"成功迁移: {success_count}")
        print(f"迁移失败: {len(failed_tables)}")
        
        if failed_tables:
            print(f"\n❌ 失败的表:")
            for table in failed_tables:
                print(f"  - {table}")
            return False
        else:
            print("\n🎉 所有表迁移成功！")
            print("\n✨ 迁移完成后的建议:")
            print("  1. 验证数据完整性")
            print("  2. 更新应用配置")
            print("  3. 重启应用服务")
            print("  4. 备份SQLite数据库作为应急恢复")
            return True
            
    except Exception as e:
        print(f"❌ 迁移过程中发生异常: {e}")
        return False
    finally:
        sqlite_conn.close()
        mysql_conn.close()

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)