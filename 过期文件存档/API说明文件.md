# 调度系统后端API说明

## 概述
本文件汇总了调度系统后端所有API接口信息，按功能模块分类展示。

---

## 1. 认证模块

### 1.1 用户登录
- **路径**: `/auth/login`
- **方法**: POST
- **功能**: 用户登录并获取访问令牌
- **权限**: 无需认证
- **请求体**:
```json
{
  "username": "admin",
  "password": "password123"
}
```
- **响应示例**:
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": 1,
    "username": "admin",
    "name": "系统管理员",
    "email": "<EMAIL>",
    "role": "admin",
    "is_active": true,
    "created_at": "2023-01-01T00:00:00"
  }
}
```

### 1.2 获取当前用户信息
- **路径**: `/auth/profile`
- **方法**: GET
- **功能**: 获取当前登录用户的详细信息
- **权限**: 需要JWT认证
- **响应示例**:
```json
{
  "user": {
    "id": 1,
    "username": "admin",
    "name": "系统管理员",
    "email": "<EMAIL>",
    "role": "admin",
    "is_active": true,
    "created_at": "2023-01-01T00:00:00"
  }
}
```

### 1.3 修改密码
- **路径**: `/auth/change-password`
- **方法**: POST
- **功能**: 修改当前登录用户的密码
- **权限**: 需要JWT认证
- **请求体**:
```json
{
  "old_password": "password123",
  "new_password": "newpassword456"
}
```
- **响应示例**:
```json
{
  "message": "密码修改成功"
}
```

## 2. 用户管理模块（/users）

### 2.1 获取用户列表
- **路径**: `/users`
- **方法**: GET
- **功能**: 获取用户列表，支持分页和搜索
- **权限**: admin, manager
- **查询参数**:
  - `page` (可选): 页码，默认1
  - `per_page` (可选): 每页条数，默认10
  - `search` (可选): 搜索关键词（匹配用户名或邮箱）
- **响应示例**:
```json
{
  "users": [
    {
      "id": 1,
      "username": "admin",
      "name": "系统管理员",
      "email": "<EMAIL>",
      "role": "admin",
      "is_active": true,
      "created_at": "2023-01-01T00:00:00"
    }
  ],
  "total": 1,
  "pages": 1,
  "current_page": 1
}
```

### 2.2 创建用户
- **路径**: `/users`
- **方法**: POST
- **功能**: 创建新用户
- **权限**: admin
- **请求体**:
```json
{
  "username": "newuser",
  "email": "<EMAIL>",
  "password": "password123",
  "name": "新用户",
  "employee_id": "EMP001",
  "phone": "13800138000",
  "position": "工程师",
  "department": "技术部",
  "role": "user",
  "id_card": "110101199001011234",
  "ethnicity": "汉",
  "gender": "male",
  "hire_date": "2023-01-01",
  "is_driver": false
}
```
- **响应示例**:
```json
{
  "user": {
    "id": 2,
    "username": "newuser",
    "name": "新用户",
    "email": "<EMAIL>",
    "role": "user",
    "is_active": true,
    "created_at": "2023-01-02T00:00:00"
  }
}
```

### 2.3 更新用户
- **路径**: `/users/{user_id}`
- **方法**: PUT
- **功能**: 更新用户信息
- **权限**: admin
- **路径参数**:
  - `user_id`: 用户ID
- **请求体**:
```json
{
  "name": "更新后的用户名",
  "position": "高级工程师",
  "role": "manager",
  "is_active": true
}
```
- **响应示例**:
```json
{
  "user": {
    "id": 2,
    "username": "newuser",
    "name": "更新后的用户名",
    "position": "高级工程师",
    "role": "manager",
    "is_active": true,
    "updated_at": "2023-01-03T00:00:00"
  }
}
```

### 2.4 删除用户
- **路径**: `/users/{user_id}`
- **方法**: DELETE
- **功能**: 删除用户
- **权限**: admin
- **路径参数**:
  - `user_id`: 用户ID
- **响应示例**:
```json
{
  "message": "用户删除成功"
}
```

## 3. 车辆管理模块（/vehicles）

### 3.1 获取车辆列表
- **路径**: `/vehicles`
- **方法**: GET
- **功能**: 获取车辆列表，支持分页、搜索、状态和分组过滤
- **权限**: admin, manager, user
- **查询参数**:
  - `page` (可选): 页码，默认1
  - `per_page` (可选): 每页条数，默认10
  - `search` (可选): 搜索关键词（匹配车牌号、品牌或型号）
  - `status` (可选): 车辆状态过滤
  - `group_id` (可选): 分组ID过滤
- **响应示例**:
```json
{
  "vehicles": [
    {
      "id": 1,
      "license_plate": "京A12345",
      "brand": "丰田",
      "model": "普拉多",
      "year": 2020,
      "vehicle_type": "SUV",
      "capacity": 5,
      "fuel_type": "汽油",
      "status": "available",
      "group_id": 1,
      "is_active": true
    }
  ],
  "total": 1,
  "pages": 1,
  "current_page": 1
}
```

### 3.2 创建车辆
- **路径**: `/vehicles`
- **方法**: POST
- **功能**: 创建新车辆
- **权限**: admin, manager
- **请求体**:
```json
{
  "license_plate": "京B67890",
  "brand": "本田",
  "model": "CR-V",
  "year": 2022,
  "vehicle_type": "SUV",
  "capacity": 5,
  "fuel_type": "混动",
  "status": "available",
  "group_id": 2
}
```
- **响应示例**:
```json
{
  "vehicle": {
    "id": 2,
    "license_plate": "京B67890",
    "brand": "本田",
    "model": "CR-V",
    "year": 2022,
    "vehicle_type": "SUV",
    "capacity": 5,
    "fuel_type": "混动",
    "status": "available",
    "group_id": 2,
    "is_active": true
  }
}
```

### 3.3 更新车辆
- **路径**: `/vehicles/{vehicle_id}`
- **方法**: PUT
- **功能**: 更新车辆信息
- **权限**: admin, manager
- **路径参数**:
  - `vehicle_id`: 车辆ID
- **请求体**:
```json
{
  "brand": "本田",
  "model": "CR-V Hybrid",
  "status": "maintenance",
  "group_id": 1
}
```
- **响应示例**:
```json
{
  "vehicle": {
    "id": 2,
    "license_plate": "京B67890",
    "brand": "本田",
    "model": "CR-V Hybrid",
    "year": 2022,
    "vehicle_type": "SUV",
    "capacity": 5,
    "fuel_type": "混动",
    "status": "maintenance",
    "group_id": 1,
    "is_active": true
  }
}
```

### 3.4 删除车辆
- **路径**: `/vehicles/{vehicle_id}`
- **方法**: DELETE
- **功能**: 删除车辆
- **权限**: admin
- **路径参数**:
  - `vehicle_id`: 车辆ID
- **响应示例**:
```json
{
  "message": "车辆删除成功"
}
```

## 4. 分组管理模块（/groups）

### 4.1 获取分组列表
- **路径**: `/groups`
- **方法**: GET
- **功能**: 获取分组列表，支持分页和搜索
- **权限**: admin, manager, user
- **查询参数**:
  - `page` (可选): 页码，默认1
  - `per_page` (可选): 每页条数，默认10
  - `search` (可选): 搜索关键词（匹配分组名称）
- **响应示例**:
```json
{
  "groups": [
    {
      "id": 1,
      "name": "技术部",
      "description": "负责技术支持的团队",
      "is_active": true,
      "created_at": "2023-01-01T00:00:00"
    }
  ],
  "total": 1,
  "pages": 1,
  "current_page": 1
}
```

### 4.2 创建分组
- **路径**: `/groups`
- **方法**: POST
- **功能**: 创建新分组
- **权限**: admin, manager
- **请求体**:
```json
{
  "name": "市场部",
  "description": "负责市场推广的团队"
}
```
- **响应示例**:
```json
{
  "group": {
    "id": 2,
    "name": "市场部",
    "description": "负责市场推广的团队",
    "is_active": true,
    "created_at": "2023-01-02T00:00:00"
  }
}
```

### 4.3 更新分组
- **路径**: `/groups/{group_id}`
- **方法**: PUT
- **功能**: 更新分组信息
- **权限**: admin, manager
- **路径参数**:
  - `group_id`: 分组ID
- **请求体**:
```json
{
  "name": "市场与销售部",
  "description": "负责市场推广和销售的团队",
  "is_active": true
}
```
- **响应示例**:
```json
{
  "group": {
    "id": 2,
    "name": "市场与销售部",
    "description": "负责市场推广和销售的团队",
    "is_active": true,
    "updated_at": "2023-01-03T00:00:00"
  }
}
```

### 4.4 删除分组
- **路径**: `/groups/{group_id}`
- **方法**: DELETE
- **功能**: 删除分组（如果分组下有用户则无法删除）
- **权限**: admin
- **路径参数**:
  - `group_id`: 分组ID
- **响应示例**:
```json
{
  "message": "分组删除成功"
}
```

## 5. 人员管理模块
### 1.1 用户登录
- 路径：`POST /auth/login`
- 功能：用户登录获取访问令牌
- 权限：无
- 请求参数：
  ```json
  {"username":"string", "password":"string"}
  ```
- 响应示例（200）：
  ```json
  {"access_token":"string", "user":{"id":"string", "username":"string", ...}}
  ```

### 1.2 获取当前用户信息
- 路径：`GET /auth/profile`
- 功能：获取登录用户详细信息
- 权限：JWT认证
- 响应示例（200）：
  ```json
  {"user":{"id":"string", "username":"string", ...}}
  ```

### 1.3 修改密码
- 路径：`POST /auth/change-password`
- 功能：修改当前用户密码
- 权限：JWT认证
- 请求参数：
  ```json
  {"old_password":"string", "new_password":"string"}
  ```
- 响应示例（200）：
  ```json
  {"message":"密码修改成功"}
  ```

---

## 2. 用户管理模块（/users）
### 2.1 获取用户列表
- 路径：`GET /users`
- 功能：分页查询用户列表（支持搜索）
- 权限：admin/manager
- 查询参数：page（页码）, per_page（每页数量）, search（搜索关键词）
- 响应示例（200）：
  ```json
  {"users":[...], "total":100, "pages":10, "current_page":1}
  ```

### 2.2 创建用户
- 路径：`POST /users`
- 功能：创建新用户
- 权限：admin
- 请求参数：
  ```json
  {"username":"string", "email":"string", "password":"string", "role":"string", "group_id":"number"}
  ```
- 响应示例（201）：
  ```json
  {"user":{"id":"number", "username":"string", ...}}
  ```



---

## 3. 分组管理模块（/groups）
### 3.1 获取分组列表
- 路径：`GET /groups`
- 功能：分页查询有效分组列表（支持搜索）
- 权限：admin/manager/user
- 查询参数：page（页码）, per_page（每页数量）, search（搜索关键词）
- 响应示例（200）：
  ```json
  {"groups":[...], "total":50, "pages":5, "current_page":1}
  ```

### 3.2 创建分组
- 路径：`POST /groups`
- 功能：创建新分组
- 权限：admin/manager
- 请求参数：
  ```json
  {"name":"string", "description":"string"}
  ```
- 响应示例（201）：
  ```json
  {"group":{"id":"number", "name":"string", ...}}
  ```

### 3.3 更新分组
- 路径：`PUT /groups/<int:group_id>`
- 功能：更新指定分组信息
- 权限：admin/manager
- 请求参数：
  ```json
  {"name":"string", "description":"string", "is_active":"boolean"}
  ```
- 响应示例（200）：
  ```json
  {"group":{"id":"number", "name":"string", ...}}
  ```

### 3.4 删除分组
- 路径：`DELETE /groups/<int:group_id>`
- 功能：删除指定分组（需无关联用户）
- 权限：admin
- 响应示例（200）：
  ```json
  {"message":"分组删除成功"}
  ```

---

## 4. 人员管理模块（/personnel）
### 4.1 获取人员列表
- 路径：`GET /personnel`
- 功能：分页查询有效人员列表（支持搜索/按组过滤）
- 权限：admin/manager/user
- 查询参数：page, per_page, search, group_id
- 响应示例（200）：
  ```json
  {"personnel":[...], "total":200, "pages":20, "current_page":1}
  ```

### 4.2 创建人员
- 路径：`POST /personnel`
- 功能：创建新人员信息
- 权限：admin/manager
- 请求参数：
  ```json
  {"name":"string", "employee_id":"string", "phone":"string", ...}
  ```
- 响应示例（201）：
  ```json
  {"personnel":{"id":"number", "name":"string", ...}}
  ```



---

## 5. 车辆管理模块（/vehicles）
### 5.1 获取车辆列表
- 路径：`GET /vehicles`
- 功能：分页查询有效车辆列表（支持搜索、状态和分组过滤）
- 权限：admin/manager/user
- 查询参数：page, per_page, search, status, group_id
- 响应示例（200）：
  ```json
  {"vehicles":[...], "total":80, "pages":8, "current_page":1}
  ```

### 5.2 创建车辆
- 路径：`POST /vehicles`
- 功能：创建新车辆信息
- 权限：admin/manager
- 请求参数：
  ```json
  {"license_plate":"string", "brand":"string", "model":"string", ...}
  ```
- 响应示例（201）：
  ```json
  {"vehicle":{"id":"number", "license_plate":"string", ...}}
  ```

### 5.3 更新车辆
- 路径：`PUT /vehicles/<int:vehicle_id>`
- 功能：更新指定车辆信息
- 权限：admin/manager
- 请求参数：
  ```json
  {"license_plate":"string", "brand":"string", "status":"string", ...}
  ```
- 响应示例（200）：
  ```json
  {"vehicle":{"id":"number", "license_plate":"string", ...}}
  ```

### 5.4 删除车辆
- 路径：`DELETE /vehicles/<int:vehicle_id>`
- 功能：删除指定车辆
- 权限：admin
- 响应示例（200）：
  ```json
  {"message":"车辆删除成功"}
  ```

---

## 6. 材料管理模块（/materials）

### 物料类别管理

#### 获取材料分类树
- **路径**: `/material-categories`
- **方法**: GET
- **功能**: 获取材料分类树结构
- **权限**: 无需特殊权限
- **响应示例**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "categories": [
      {
        "id": 1,
        "name": "原材料",
        "code": "RAW",
        "parent_id": null,
        "level": 1,
        "path": "RAW",
        "sort_order": 0,
        "description": "基础原材料",
        "is_active": true,
        "created_at": "2023-01-01T00:00:00",
        "updated_at": "2023-01-01T00:00:00",
        "children": [
          {
            "id": 2,
            "name": "金属材料",
            "code": "METAL",
            "parent_id": 1,
            "level": 2,
            "path": "RAW/METAL",
            "sort_order": 0,
            "description": "各类金属材料",
            "is_active": true,
            "created_at": "2023-01-01T00:00:00",
            "updated_at": "2023-01-01T00:00:00",
            "children": []
          }
        ]
      }
    ],
    "total": 2
  }
}
```

#### 获取扁平化分类列表
- **路径**: `/material-categories/flat`
- **方法**: GET
- **功能**: 获取扁平化的分类列表
- **权限**: 无需特殊权限
- **查询参数**:
  - `level` (可选): 分类层级
  - `parent_id` (可选): 父分类ID
- **响应示例**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "categories": [
      {
        "id": 1,
        "name": "原材料",
        "code": "RAW",
        "parent_id": null,
        "level": 1,
        "path": "RAW",
        "sort_order": 0,
        "description": "基础原材料"
      },
      {
        "id": 2,
        "name": "金属材料",
        "code": "METAL",
        "parent_id": 1,
        "level": 2,
        "path": "RAW/METAL",
        "sort_order": 0,
        "description": "各类金属材料"
      }
    ]
  }
}
```

#### 获取分类管理列表（管理员用）
- **路径**: `/material-categories/manage`
- **方法**: GET
- **功能**: 获取分类管理列表，支持分页、搜索和层级过滤
- **权限**: admin, manager
- **查询参数**:
  - `page` (可选): 页码，默认1
  - `per_page` (可选): 每页条数，默认20
  - `search` (可选): 搜索关键词
  - `level` (可选): 分类层级
- **响应示例**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "categories": [
      {
        "id": 1,
        "name": "原材料",
        "code": "RAW",
        "parent_id": null,
        "parent_name": null,
        "level": 1,
        "path": "RAW",
        "sort_order": 0,
        "description": "基础原材料",
        "is_active": true,
        "material_count": 10,
        "created_at": "2023-01-01T00:00:00",
        "updated_at": "2023-01-01T00:00:00"
      }
    ],
    "total": 1,
    "page": 1,
    "per_page": 20,
    "pages": 1
  }
}
```

#### 创建材料分类
- **路径**: `/material-categories`
- **方法**: POST
- **功能**: 创建新的材料分类
- **权限**: admin, manager
- **请求体**:
```json
{
  "name": "非金属材料",
  "code": "NONMETAL",
  "parent_id": 1,
  "sort_order": 1,
  "description": "各类非金属材料",
  "is_active": true
}
```
- **响应示例**:
```json
{
  "code": 201,
  "message": "分类创建成功"
}
```

#### 更新材料分类
- **路径**: `/material-categories/{category_id}`
- **方法**: PUT
- **功能**: 更新材料分类信息
- **权限**: admin, manager
- **请求体**:
```json
{
  "name": "非金属材料更新",
  "code": "NONMETAL-UPDATED",
  "sort_order": 2,
  "description": "更新后的非金属材料描述",
  "is_active": true
}
```
- **响应示例**:
```json
{
  "code": 200,
  "message": "分类更新成功"
}
```

#### 删除材料分类
- **路径**: `/material-categories/{category_id}`
- **方法**: DELETE
- **功能**: 删除材料分类
- **权限**: admin
- **响应示例**:
```json
{
  "code": 200,
  "message": "分类删除成功"
}
```

#### 获取分类下的材料列表
- **路径**: `/material-categories/{category_id}/materials`
- **方法**: GET
- **功能**: 获取指定分类下的材料列表
- **权限**: 无需特殊权限
- **查询参数**:
  - `page` (可选): 页码，默认1
  - `per_page` (可选): 每页条数，默认20
- **响应示例**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "materials": [
      {
        "id": 1,
        "name": "钢板",
        "code": "STEEL-001",
        "category": "金属材料",
        "stock_quantity": 100,
        "min_stock": 20,
        "unit": "张",
        "price": 500.00
      }
    ],
    "total": 1,
    "page": 1,
    "per_page": 20,
    "pages": 1
  }
}
```

### 6.1 获取材料列表
- 路径：`GET /materials`
- 功能：分页查询有效材料列表（支持搜索、分类和低库存过滤）
- 权限：admin/manager/user
- 查询参数：page, per_page, search, category, low_stock
- 响应示例（200）：
  ```json
  {"materials":[...], "total":150, "pages":15, "current_page":1}
  ```

### 6.2 创建材料
- 路径：`POST /materials`
- 功能：创建新材料信息
- 权限：admin/manager
- 请求参数：
  ```json
  {"name":"string", "code":"string", "category":"string", ...}
  ```
- 响应示例（201）：
  ```json
  {"material":{"id":"number", "name":"string", ...}}
  ```

### 6.3 更新材料
- 路径：`PUT /materials/<int:material_id>`
- 功能：更新指定材料信息
- 权限：admin/manager
- 请求参数：
  ```json
  {"name":"string", "code":"string", "stock_quantity":"number", ...}
  ```
- 响应示例（200）：
  ```json
  {"material":{"id":"number", "name":"string", ...}}
  ```

### 6.4 删除材料
- 路径：`DELETE /materials/<int:material_id>`
- 功能：删除指定材料
- 权限：admin
- 响应示例（200）：
  ```json
  {"message":"材料删除成功"}
  ```

### 6.5 获取材料分类列表
- 路径：`GET /materials/categories`
- 功能：获取所有有效材料分类
- 权限：admin/manager/user
- 响应示例（200）：
  ```json
  {"categories":["五金","化工","电子"]}
  ```

---

## 7. 工单管理模块（/work-orders）
### 7.1 获取工单列表
- 路径：`GET /work-orders`
- 功能：分页查询工单列表（支持搜索、状态、优先级、分配人过滤）
- 权限：admin/manager/user（根据角色限制可见范围）
- 查询参数：page, per_page, search, status, priority, assignee_id, group_id
- 响应示例（200）：
  ```json
  {"work_orders":[...], "total":300, "pages":30, "current_page":1}
  ```

### 7.2 创建工单
- 路径：`POST /work-orders`
- 功能：创建新工单（含自定义字段）
- 权限：admin/manager/user
- 请求参数：
  ```json
  {"title":"string", "description":"string", "priority":"string", "custom_fields":{...}}
  ```
- 响应示例（201）：
  ```json
  {"work_order":{"id":"number", "order_number":"WO20240612153045", ...}}
  ```

### 7.3 获取工单详情
- 路径：`GET /work-orders/<int:order_id>`
- 功能：获取指定工单详细信息（含自定义字段值）
- 权限：admin/manager/user（根据角色限制访问）
- 响应示例（200）：
  ```json
  {"work_order":{"id":"number", "title":"设备维修", "custom_fields":{...}}}
  ```

### 7.4 更新工单
- 路径：`PUT /work-orders/<int:order_id>`
- 功能：更新工单基本信息及自定义字段值
- 权限：admin/manager/user（根据角色限制修改）
- 请求参数：
  ```json
  {"title":"string", "status":"in_progress", "custom_fields":{...}}
  ```
- 响应示例（200）：
  ```json
  {"work_order":{"id":"number", "status":"in_progress", ...}}
  ```

### 7.5 删除工单
- 路径：`DELETE /work-orders/<int:order_id>`
- 功能：删除指定工单
- 权限：admin/manager（根据角色限制删除）
- 响应示例（200）：
  ```json
  {"message":"工单删除成功"}
  ```

### 7.6 管理工单自定义字段
#### 7.6.1 获取自定义字段列表
- 路径：`GET /work-orders/fields`
- 功能：获取所有有效自定义字段
- 权限：admin/manager/user
- 响应示例（200）：
  ```json
  {"fields":[{"name":"location", "label":"地点", ...}]}
  ```

#### 7.6.2 创建自定义字段
- 路径：`POST /work-orders/fields`
- 功能：创建新自定义字段
- 权限：admin/manager
- 请求参数：
  ```json
  {"name":"location", "label":"地点", "field_type":"text"}
  ```
- 响应示例（201）：
  ```json
  {"field":{"id":"number", "name":"location", ...}}
  ```

#### 7.6.3 更新自定义字段
- 路径：`PUT /work-orders/fields/<int:field_id>`
- 功能：更新指定自定义字段
- 权限：admin/manager
- 请求参数：
  ```json
  {"label":"维修地点", "is_required":true}
  ```
- 响应示例（200）：
  ```json
  {"field":{"id":"number", "label":"维修地点", ...}}
  ```

#### 7.6.4 删除自定义字段
- 路径：`DELETE /work-orders/fields/<int:field_id>`
- 功能：删除指定自定义字段（级联删除关联值）
- 权限：admin
- 响应示例（200）：
  ```json
  {"message":"字段删除成功"}
  ```

### 7.7 获取工单统计数据
- 路径：`GET /work-orders/stats`
- 功能：统计各状态工单数量
- 权限：admin/manager/user（根据角色限制统计范围）
- 响应示例（200）：
  ```json
  {"total":300, "pending":50, "in_progress":120, "completed":100, "cancelled":30}
  ```

### 7.8 工单处理流程
#### 7.8.1 获取工单处理记录
- 路径：`GET /work-orders/<int:work_order_id>/process-records`
- 功能：获取指定工单的处理记录列表
- 权限：admin/manager/user（根据角色限制访问）
- 查询参数：page, per_page, action_type, user_id
- 响应示例（200）：
  ```json
  {"process_records":[{"id":1, "action":"接单", "description":"接受工单任务", ...}], "pagination":{"page":1, "total":10}}
  ```

#### 7.8.2 创建工单处理记录
- 路径：`POST /work-orders/<int:work_order_id>/process-records`
- 功能：为指定工单添加处理记录，支持同时更新工单状态
- 权限：admin/manager/user（根据角色限制操作）
- 请求参数：
  ```json
  {"action":"接单", "description":"接受工单任务", "update_status":true, "new_status":"in_progress"}
  ```
- 响应示例（201）：
  ```json
  {"process_record":{"id":1, "action":"接单", ...}, "message":"处理记录创建成功"}
  ```

#### 7.8.3 更新处理记录
- 路径：`PUT /process-records/<int:record_id>`
- 功能：更新指定处理记录
- 权限：admin/manager或记录创建者
- 请求参数：
  ```json
  {"action":"开始处理", "description":"开始进行设备维修"}
  ```
- 响应示例（200）：
  ```json
  {"process_record":{"id":1, "action":"开始处理", ...}, "message":"处理记录更新成功"}
  ```

#### 7.8.4 删除处理记录
- 路径：`DELETE /process-records/<int:record_id>`
- 功能：删除指定处理记录
- 权限：admin/manager
- 响应示例（200）：
  ```json
  {"message":"处理记录删除成功"}
  ```

#### 7.8.5 获取工单时间线
- 路径：`GET /work-orders/<int:work_order_id>/timeline`
- 功能：获取工单完整处理时间线（创建、处理记录、状态变更）
- 权限：admin/manager/user（根据角色限制访问）
- 响应示例（200）：
  ```json
  {"timeline":[{"type":"created", "action":"工单创建", ...}, {...}], "work_order":{"id":1, "title":"设备维修"}}
  ```

#### 7.8.6 获取可用处理操作类型
- 路径：`GET /process-records/actions`
- 功能：获取系统预定义的工单处理操作类型列表
- 权限：admin/manager/user
- 响应示例（200）：
  ```json
  {"actions":[{"value":"接单", "label":"接单", "description":"接受工单任务"}, ...]}
  ```

#### 7.8.7 工单快速操作
- 路径：`POST /work-orders/<int:work_order_id>/quick-actions`
- 功能：执行工单快速操作（简化的状态变更流程）
- 权限：admin/manager/user（根据角色限制操作）
- 请求参数：
  ```json
  {"action":"accept", "description":"快速接单处理"}
  ```
- 响应示例（200）：
  ```json
  {"work_order":{"id":1, "status":"in_progress"}, "message":"操作执行成功"}
  ```"}}]}}

### 7.9 工单分析与报表
#### 7.9.1 获取工单统计分析
- 路径：`GET /work-orders/analytics/stats`
- 功能：获取工单详细统计数据，包括状态分布、优先级分布、完成率等
- 权限：admin/manager/user（根据角色限制统计范围）
- 响应示例（200）：
  ```json
  {
    "total": 300,
    "status_stats": {"pending": 50, "assigned": 30, "in_progress": 90, "completed": 100, "cancelled": 30},
    "priority_stats": {"urgent": 40, "high": 80, "medium": 120, "low": 60},
    "completion_rate": 33.3,
    "today_new": 15,
    "week_new": 85,
    "overdue": 12
  }
  ```

#### 7.9.2 获取工单报表数据
- 路径：`GET /work-orders/analytics/reports`
- 功能：获取工单报表数据，支持按日/周/月维度和不同报表类型
- 权限：admin/manager/user（根据角色限制访问范围）
- 查询参数：start_date（开始日期）, end_date（结束日期）, dimension（维度：day/week/month）, type（类型：overview/trend/performance）
- 响应示例（200）：
  ```json
  {
    "overview": {
      "total": 250,
      "completed": 180,
      "pending": 30,
      "processing": 35,
      "cancelled": 5,
      "completion_rate": 72.0
    },
    "status_distribution": {"pending": 30, "assigned": 15, "in_progress": 20, "completed": 180, "cancelled": 5},
    "priority_distribution": {"low": 50, "medium": 100, "high": 70, "urgent": 30}
  }
  ```

### 7.10 工单材料管理
#### 7.10.1 获取工单材料使用列表
- 路径：`GET /work-orders/<int:work_order_id>/materials`
- 功能：获取指定工单的材料使用记录列表
- 权限：admin/manager/user（根据角色限制访问）
- 响应示例（200）：
  ```json
  {"materials":[{"id":1, "material_id":5, "quantity":10, "unit_price":25.5, "total_price":255, ...}]}
  ```

#### 7.10.2 批量添加材料使用记录
- 路径：`POST /work-orders/<int:work_order_id>/materials`
- 功能：为工单批量添加材料使用记录，自动扣减库存
- 权限：admin/manager/user（根据角色限制操作）
- 请求参数：
  ```json
  {"materials":[{"material_id":5, "quantity":10, "notes":"维修用"}, ...]}
  ```
- 响应示例（201）：
  ```json
  {"message":"成功添加 2 个材料使用记录", "materials":[...]}
  ```

#### 7.10.3 更新材料使用记录
- 路径：`PUT /work-orders/<int:work_order_id>/materials/<int:material_id>`
- 功能：更新工单的特定材料使用记录
- 权限：admin/manager/user（根据角色限制操作）
- 请求参数：
  ```json
  {"quantity":15, "notes":"更新数量"}
  ```
- 响应示例（200）：
  ```json
  {"message":"材料使用记录更新成功", "material_usage":{...}}
  ```

#### 7.10.4 删除材料使用记录
- 路径：`DELETE /material-usage/<int:usage_id>`
- 功能：删除指定的材料使用记录，自动恢复库存
- 权限：admin/manager/user（根据角色限制操作）
- 响应示例（200）：
  ```json
  {"message":"材料使用记录删除成功"}
  ```

## 区域管理模块

#### 1. 获取区域列表
- **路径**: `/areas/`
- **方法**: GET
- **权限**: 管理员、经理、普通用户
- **描述**: 获取区域列表，支持分页、搜索和包含成员信息
- **参数**: 
  - `page`: 页码(默认1)
  - `per_page`: 每页条数(默认10，-1表示获取所有)
  - `search`: 搜索关键词(可选，搜索区域名称)
  - `include_members`: 是否包含成员信息(默认false)
- **返回值**: 区域列表、总数、页码、每页条数和总页数

#### 2. 获取单个区域详情
- **路径**: `/areas/<int:area_id>`
- **方法**: GET
- **权限**: 管理员、经理、普通用户
- **描述**: 获取指定ID的区域详情
- **路径参数**: `area_id`: 区域ID
- **参数**: 
  - `include_members`: 是否包含成员信息(默认true)
- **返回值**: 区域详细信息

#### 3. 获取区域选项列表
- **路径**: `/areas/options`
- **方法**: GET
- **权限**: 管理员、经理、普通用户
- **描述**: 获取区域选项列表，用于下拉选择
- **返回值**: 包含区域ID和名称的选项列表

#### 4. 创建新区域
- **路径**: `/areas/`
- **方法**: POST
- **权限**: 管理员
- **描述**: 创建新的区域
- **请求体**: 
  - `name`: 区域名称(必填)
  - `leader_id`: 组长ID(可选)
  - `description`: 描述(可选)
  - `is_active`: 是否激活(默认true)
- **返回值**: 创建成功消息和区域信息

#### 5. 更新区域信息
- **路径**: `/areas/<int:area_id>`
- **方法**: PUT
- **权限**: 管理员
- **描述**: 更新指定ID的区域信息
- **路径参数**: `area_id`: 区域ID
- **请求体**: 
  - `name`: 区域名称(必填)
  - `leader_id`: 组长ID(可选)
  - `description`: 描述(可选)
  - `is_active`: 是否激活(默认true)
- **返回值**: 更新成功消息和区域信息

#### 6. 删除区域
- **路径**: `/areas/<int:area_id>`
- **方法**: DELETE
- **权限**: 管理员
- **描述**: 软删除指定ID的区域
- **路径参数**: `area_id`: 区域ID
- **返回值**: 删除成功消息

#### 7. 获取区域成员列表
- **路径**: `/areas/<int:area_id>/members`
- **方法**: GET
- **权限**: 管理员、经理、普通用户
- **描述**: 获取指定区域的成员列表
- **路径参数**: `area_id`: 区域ID
- **返回值**: 区域信息和成员列表

## 仪表盘模块

#### 1. 获取仪表板统计数据
- **路径**: `/dashboard/stats`
- **方法**: GET
- **权限**: 管理员、经理、普通用户
- **描述**: 获取系统统计数据，包括工单、人员、车辆和材料等统计信息
- **返回值**: 
  - `totalWorkOrders`: 总工单数
  - `pendingWorkOrders`: 待处理工单数
  - `inProgressWorkOrders`: 处理中工单数
  - `completedWorkOrders`: 已完成工单数
  - `totalUsers`: 总用户数
  - `totalVehicles`: 总车辆数
  - `availableVehicles`: 可用车辆数
  - `totalMaterials`: 总材料数
  - `lowStockMaterials`: 低库存材料数
  - `dailyTrend`: 最近7天工单趋势
  - `statusDistribution`: 工单状态分布
  - `priorityDistribution`: 工单优先级分布

#### 2. 获取最近工单
- **路径**: `/dashboard/recent-work-orders`
- **方法**: GET
- **权限**: 管理员、经理、普通用户
- **描述**: 获取最近的5条工单信息
- **返回值**: 包含工单ID、标题、优先级、状态、创建时间、创建人姓名和负责人姓名的列表

## 物料使用管理模块

#### 1. 获取材料使用记录列表
- **路径**: `/material_usage`
- **方法**: GET
- **权限**: 管理员、经理、普通用户
- **描述**: 获取材料使用记录列表，支持分页、搜索和多条件过滤
- **参数**: 
  - `page`: 页码(默认1)
  - `per_page`: 每页条数(默认20)
  - `material_name`: 材料名称(可选)
  - `user_name`: 用户名(可选)
  - `usage_type`: 使用类型(可选)
  - `start_date`: 开始日期(YYYY-MM-DD，可选)
  - `end_date`: 结束日期(YYYY-MM-DD，可选)
- **返回值**: 记录列表、总数、总页数、当前页码和每页条数

#### 2. 获取单个使用记录详情
- **路径**: `/material_usage/<int:usage_id>`
- **方法**: GET
- **权限**: 管理员、经理、普通用户(仅自己创建的记录)
- **描述**: 获取指定ID的材料使用记录详情
- **路径参数**: `usage_id`: 使用记录ID
- **返回值**: 包含材料信息的使用记录详情

#### 3. 创建使用记录
- **路径**: `/material_usage`
- **方法**: POST
- **权限**: 管理员、经理、普通用户
- **描述**: 创建新的材料使用记录，会减少相应材料库存
- **请求体**: 
  - `material_id`: 材料ID(必填)
  - `quantity`: 使用数量(必填)
  - `usage_type`: 使用类型(必填)
  - `user_name`: 用户名(必填)
  - `usage_date`: 使用日期(YYYY-MM-DD，必填)
  - `purpose`: 使用目的(必填)
  - `work_order_id`: 工单ID(可选)
  - `notes`: 备注(可选)
- **返回值**: 创建成功消息和使用记录信息

#### 4. 更新使用记录
- **路径**: `/material_usage/<int:usage_id>`
- **方法**: PUT
- **权限**: 管理员、经理、普通用户(仅自己创建的记录)
- **描述**: 更新指定ID的使用记录，若修改数量会调整材料库存
- **路径参数**: `usage_id`: 使用记录ID
- **请求体**: 
  - `quantity`: 使用数量
  - `usage_type`: 使用类型
  - `user_name`: 用户名
  - `usage_date`: 使用日期(YYYY-MM-DD)
  - `purpose`: 使用目的
  - `notes`: 备注
- **返回值**: 更新成功消息和使用记录信息

#### 5. 删除使用记录
- **路径**: `/material_usage/<int:usage_id>`
- **方法**: DELETE
- **权限**: 管理员、经理、普通用户(仅自己创建的记录)
- **描述**: 删除指定ID的使用记录，会归还材料库存
- **路径参数**: `usage_id`: 使用记录ID
- **返回值**: 删除成功消息

#### 6. 获取使用记录统计数据
- **路径**: `/material_usage/stats`
- **方法**: GET
- **权限**: 管理员、经理、普通用户
- **描述**: 获取材料使用记录的统计数据
- **返回值**: 使用记录统计信息

## 物料管理模块

#### 1. 获取材料列表
- **路径**: `/materials`
- **方法**: GET
- **权限**: 管理员、经理、普通用户
- **描述**: 获取材料列表，支持分页、搜索和多条件过滤
- **参数**: 
  - `page`: 页码(默认1)
  - `per_page`: 每页条数(默认10)
  - `search`: 搜索关键词(可选，搜索名称、编码或供应商)
  - `category`: 材料分类(可选)
  - `low_stock`: 是否只显示低库存材料(可选，布尔值)
- **返回值**: 材料列表、总数、总页数和当前页码

#### 2. 创建材料
- **路径**: `/materials`
- **方法**: POST
- **权限**: 管理员、经理
- **描述**: 创建新的材料
- **请求体**: 
  - `name`: 材料名称(必填)
  - `code`: 材料编码(可选，需唯一)
  - `category`: 材料分类(可选)
  - `unit`: 单位(可选)
  - `specification`: 规格(可选)
  - `stock_quantity`: 库存数量(默认0)
  - `min_stock`: 最低库存(默认0)
  - `unit_price`: 单价(可选)
  - `supplier`: 供应商(可选)
- **返回值**: 创建的材料信息

#### 3. 更新材料
- **路径**: `/materials/<int:material_id>`
- **方法**: PUT
- **权限**: 管理员、经理
- **描述**: 更新指定ID的材料信息
- **路径参数**: `material_id`: 材料ID
- **请求体**: 同创建接口字段
- **返回值**: 更新后的材料信息

#### 4. 删除材料
- **路径**: `/materials/<int:material_id>`
- **方法**: DELETE
- **权限**: 管理员
- **描述**: 删除指定ID的材料
- **路径参数**: `material_id`: 材料ID
- **返回值**: 删除成功消息

#### 5. 更新物料库存
- **路径**: `/materials/<int:material_id>/stock`
- **方法**: POST
- **权限**: 管理员、经理
- **描述**: 更新物料库存(入库或出库)
- **路径参数**: `material_id`: 材料ID
- **请求体**: 
  - `operation`: 操作类型(in/out，必填)
  - `quantity`: 数量(必填，大于0)
  - `reason`: 操作原因(可选)
- **返回值**: 操作成功消息和更新后的材料信息

#### 6. 获取材料分类列表
- **路径**: `/materials/categories`
- **方法**: GET
- **权限**: 管理员、经理、普通用户
- **描述**: 获取所有材料分类
- **返回值**: 材料分类列表

## 数据字典管理模块

### 表单模板管理模块

#### 1. 获取指定类型的表单模板
- **路径**: `/form_templates/<form_type>`
- **方法**: GET
- **权限**: 无需认证
- **描述**: 获取指定类型的表单模板字段列表
- **参数**: 
  - `form_type` (路径参数): 表单类型
- **返回值**: 表单类型、字段列表及总数

#### 2. 获取所有表单类型
- **路径**: `/form_templates/types`
- **方法**: GET
- **权限**: 无需认证
- **描述**: 获取系统中所有可用的表单类型
- **返回值**: 表单类型列表及每个类型的字段数量

#### 3. 获取表单模板管理列表
- **路径**: `/form_templates/manage`
- **方法**: GET
- **权限**: 管理员、经理
- **描述**: 获取表单模板管理列表，支持分页、搜索和按类型过滤
- **参数**: 
  - `page`: 页码(默认1)
  - `per_page`: 每页条数(默认20)
  - `form_type`: 表单类型(可选)
  - `search`: 搜索关键词(可选，搜索字段名或标签)
- **返回值**: 模板列表、总数、页码、每页条数和总页数

#### 4. 创建表单字段模板
- **路径**: `/form_templates`
- **方法**: POST
- **权限**: 管理员、经理
- **描述**: 创建新的表单字段模板
- **请求体**: 
  - `form_type`: 表单类型(必填)
  - `field_name`: 字段名(必填)
  - `field_label`: 字段标签(必填)
  - `field_type`: 字段类型(必填)
  - `field_options`: 字段选项(JSON格式)
  - `validation_rules`: 验证规则(JSON格式)
  - `default_value`: 默认值
  - `placeholder`: 占位符
  - `help_text`: 帮助文本
  - `sort_order`: 排序号
  - `is_required`: 是否必填
  - `is_active`: 是否激活
- **返回值**: 创建成功消息

#### 5. 更新表单字段模板
- **路径**: `/form_templates/<template_id>`
- **方法**: PUT
- **权限**: 管理员、经理
- **描述**: 更新指定ID的表单字段模板
- **路径参数**: `template_id`: 模板ID
- **请求体**: 可更新字段同创建接口(不含form_type和field_name)
- **返回值**: 更新成功消息

#### 6. 删除表单字段模板
- **路径**: `/form_templates/<template_id>`
- **方法**: DELETE
- **权限**: 管理员
- **描述**: 删除指定ID的表单字段模板
- **路径参数**: `template_id`: 模板ID
- **返回值**: 删除成功消息

#### 7. 批量更新表单模板
- **路径**: `/form_templates/batch`
- **方法**: POST
- **权限**: 管理员、经理(删除操作仅限管理员)
- **描述**: 批量激活、停用或删除表单模板
- **请求体**: 
  - `ids`: 模板ID列表
  - `action`: 操作类型(activate/deactivate/delete)
- **返回值**: 批量操作成功消息

#### 8. 复制表单模板
- **路径**: `/form_templates/copy`
- **方法**: POST
- **权限**: 管理员、经理
- **描述**: 复制现有模板创建新模板
- **请求体**: 
  - `source_id`: 源模板ID
  - `new_form_type`: 新表单类型
  - `new_field_name`: 新字段名
- **返回值**: 复制成功消息

## 数据字典管理模块

### 7.1 获取指定类型的字典数据
- **路径**: `/dictionaries/{dict_type}`
- **方法**: GET
- **功能**: 获取指定类型的字典数据列表
- **权限**: 无需特殊权限
- **路径参数**:
  - `dict_type`: 字典类型编码（如：WORK_ORDER_STATUS, PRIORITY_LEVEL）
- **响应示例**:
```json
{
  "dictionaries": [
    {
      "id": 1,
      "dict_type": "WORK_ORDER_STATUS",
      "dict_key": "PENDING",
      "dict_label": "待处理",
      "dict_value": "pending",
      "sort_order": 1,
      "is_active": true,
      "is_system": true,
      "parent_id": null,
      "description": "工单初始状态"
    },
    {
      "id": 2,
      "dict_type": "WORK_ORDER_STATUS",
      "dict_key": "PROCESSING",
      "dict_label": "处理中",
      "dict_value": "processing",
      "sort_order": 2,
      "is_active": true,
      "is_system": true,
      "parent_id": null,
      "description": "工单处理中状态"
    }
  ],
  "total": 2
}
```

### 7.2 获取所有字典类型
- **路径**: `/dictionaries/types`
- **方法**: GET
- **功能**: 获取系统中所有字典类型及其条目数量
- **权限**: 无需特殊权限
- **响应示例**:
```json
{
  "types": [
    {
      "dict_type": "WORK_ORDER_STATUS",
      "count": 5
    },
    {
      "dict_type": "PRIORITY_LEVEL",
      "count": 3
    },
    {
      "dict_type": "MATERIAL_UNIT",
      "count": 10
    }
  ]
}
```

### 7.3 获取所有字典数据（管理员用）
- **路径**: `/dictionaries`
- **方法**: GET
- **功能**: 获取系统中所有字典数据，支持分页、类型过滤和关键词搜索
- **权限**: admin
- **查询参数**:
  - `page` (可选): 页码，默认1
  - `per_page` (可选): 每页条数，默认20
  - `dict_type` (可选): 字典类型过滤
  - `search` (可选): 搜索关键词（匹配dict_key或dict_label）
- **响应示例**:
```json
{
  "dictionaries": [
    {
      "id": 1,
      "dict_type": "WORK_ORDER_STATUS",
      "dict_key": "PENDING",
      "dict_label": "待处理",
      "dict_value": "pending",
      "sort_order": 1,
      "is_active": true,
      "is_system": true,
      "parent_id": null,
      "description": "工单初始状态",
      "created_at": "2023-01-01T00:00:00",
      "updated_at": "2023-01-01T00:00:00"
    }
  ],
  "total": 1,
  "page": 1,
  "per_page": 20,
  "pages": 1
}
```

### 7.4 创建字典项
- **路径**: `/dictionaries`
- **方法**: POST
- **功能**: 创建新的字典项
- **权限**: admin
- **请求体**:
```json
{
  "dict_type": "WORK_ORDER_STATUS",
  "dict_key": "CANCELED",
  "dict_label": "已取消",
  "dict_value": "canceled",
  "sort_order": 5,
  "is_active": true,
  "is_system": false,
  "parent_id": null,
  "description": "工单取消状态"
}
```
- **响应示例**:
```json
{
  "message": "字典项创建成功"
}
```

### 7.5 更新字典项
- **路径**: `/dictionaries/{dict_id}`
- **方法**: PUT
- **功能**: 更新现有字典项
- **权限**: admin
- **路径参数**:
  - `dict_id`: 字典项ID
- **请求体**:
```json
{
  "dict_label": "已取消（作废）",
  "sort_order": 6,
  "is_active": false,
  "description": "工单取消状态（已作废）"
}
```
- **响应示例**:
```json
{
  "message": "字典项更新成功"
}
```

### 7.6 删除字典项
- **路径**: `/dictionaries/{dict_id}`
- **方法**: DELETE
- **功能**: 删除字典项（系统内置字典项不可删除）
- **权限**: admin
- **路径参数**:
  - `dict_id`: 字典项ID
- **响应示例**:
```json
{
  "message": "字典项删除成功"
}
```

## 8. 系统配置模块（/system-configs）
### 8.1 获取系统配置列表
- 路径：`GET /system-configs`
- 功能：获取系统配置项列表，支持分类和搜索过滤
- 权限：admin/manager可查看所有配置，普通用户仅可查看公开配置
- 查询参数：category（分类）, search（搜索关键词）, is_public（是否公开）
- 响应示例（200）：
  ```json
  {
    "configs": [
      {
        "id": 1,
        "config_key": "system_name",
        "config_value": "调度管理系统",
        "category": "system",
        "description": "系统名称",
        "data_type": "string",
        "is_public": true,
        "is_editable": true
      },
      ...
    ],
    "total": 20
  }
  ```

### 8.2 获取配置分类列表
- 路径：`GET /system-configs/categories`
- 功能：获取所有配置分类及各分类下的配置数量
- 权限：admin/manager可查看所有分类，普通用户仅可查看包含公开配置的分类
- 响应示例（200）：
  ```json
  {
    "categories": [
      {"category": "system", "config_count": 10, "active_count": 10},
      {"category": "work_order", "config_count": 15, "active_count": 15}
    ]
  }
  ```

### 8.3 根据配置键获取配置值
- 路径：`GET /system-configs/key/<config_key>`
- 功能：根据配置键获取单个配置项的详细信息和值
- 权限：admin/manager可查看所有配置，普通用户仅可查看公开配置
- 响应示例（200）：
  ```json
  {
    "config_key": "system_name",
    "config_value": "调度管理系统",
    "data_type": "string"
  }
  ```

### 8.4 批量获取配置值
- 路径：`POST /system-configs/batch`
- 功能：根据配置键列表批量获取多个配置项的值
- 权限：admin/manager可获取所有配置，普通用户仅可获取公开配置
- 请求参数：
  ```json
  {"config_keys": ["system_name", "max_upload_size", "enable_notification"]}
  ```
- 响应示例（200）：
  ```json
  {
    "configs": {
      "system_name": "调度管理系统",
      "max_upload_size": 10,
      "enable_notification": true
    }
  }
  ```

### 8.5 创建系统配置
- 路径：`POST /system-configs`
- 功能：创建新的系统配置项
- 权限：仅admin
- 请求参数：
  ```json
  {
    "config_key": "new_config",
    "config_value": "config_value",
    "category": "system",
    "description": "新配置项",
    "data_type": "string",
    "is_public": true,
    "is_editable": true
  }
  ```
- 响应示例（201）：
  ```json
  {
    "id": 21,
    "config_key": "new_config",
    "message": "配置创建成功"
  }
  ```

### 8.6 更新系统配置
- 路径：`PUT /system-configs/<int:config_id>`
- 功能：更新指定配置项的值或属性
- 权限：仅admin
- 请求参数：
  ```json
  {
    "config_value": "updated_value",
    "description": "更新后的描述",
    "is_editable": false
  }
  ```
- 响应示例（200）：
  ```json
  {
    "id": 21,
    "message": "配置更新成功"
  }
  ```

### 8.7 删除系统配置
- 路径：`DELETE /system-configs/<int:config_id>`
- 功能：删除指定的系统配置项
- 权限：仅admin
- 响应示例（200）：
  ```json
  {"message": "配置删除成功"}
  ```

## 9. 区域管理模块（/areas）
### 9.1 获取区域列表
- 路径：`GET /areas`
- 功能：获取区域列表，支持分页、搜索和成员信息包含
- 权限：admin/manager/user
- 查询参数：page（页码）, per_page（每页数量，-1表示全部）, search（搜索关键词）, include_members（是否包含成员）
- 响应示例（200）：
  ```json
  {
    "areas": [
      {
        "id": 1,
        "name": "东区",
        "leader_id": 5,
        "description": "负责东部区域调度",
        "is_active": true
      },
      ...
    ],
    "total": 10,
    "page": 1,
    "per_page": 10,
    "pages": 1
  }
  ```

### 9.2 获取单个区域详情
- 路径：`GET /areas/<int:area_id>`
- 功能：获取指定区域的详细信息
- 权限：admin/manager/user
- 查询参数：include_members（是否包含成员，默认true）
- 响应示例（200）：
  ```json
  {
    "id": 1,
    "name": "东区",
    "leader_id": 5,
    "leader_name": "张三",
    "description": "负责东部区域调度",
    "is_active": true,
    "members": [...]
  }
  ```

### 9.3 获取区域选项列表
- 路径：`GET /areas/options`
- 功能：获取区域选项列表（用于下拉选择）
- 权限：admin/manager/user
- 响应示例（200）：
  ```json
  [
    {"value": 1, "label": "东区"},
    {"value": 2, "label": "西区"}
  ]
  ```

### 9.4 创建区域
- 路径：`POST /areas`
- 功能：创建新区域
- 权限：仅admin
- 请求参数：
  ```json
  {
    "name": "北区",
    "leader_id": 6,
    "description": "负责北部区域调度",
    "is_active": true
  }
  ```
- 响应示例（201）：
  ```json
  {
    "message": "区域创建成功",
    "area": {
      "id": 3,
      "name": "北区",
      "leader_id": 6,
      "description": "负责北部区域调度",
      "is_active": true
    }
  }
  ```

### 9.5 更新区域
- 路径：`PUT /areas/<int:area_id>`
- 功能：更新指定区域信息
- 权限：仅admin
- 请求参数：
  ```json
  {
    "name": "北区更新",
    "leader_id": 7,
    "description": "更新后的区域描述",
    "is_active": true
  }
  ```
- 响应示例（200）：
  ```json
  {
    "message": "区域更新成功",
    "area": {
      "id": 3,
      "name": "北区更新",
      "leader_id": 7,
      "description": "更新后的区域描述",
      "is_active": true
    }
  }
  ```

### 9.6 删除区域
- 路径：`DELETE /areas/<int:area_id>`
- 功能：软删除指定区域（仅当区域无成员时可删除）
- 权限：仅admin
- 响应示例（200）：
  ```json
  {"message": "区域删除成功"}
  ```

### 9.7 获取区域成员列表
- 路径：`GET /areas/<int:area_id>/members`
- 功能：获取指定区域的成员列表
- 权限：admin/manager/user
- 响应示例（200）：
  ```json
  {
    "area": {"id": 1, "name": "东区"},
    "members": [
      {
        "id": 5,
        "name": "张三",
        "username": "zhangsan",
        "position": "区域主管",
        "role": "manager"
      },
      ...
    ],
    "total": 8
  }
  ```

## 接口通用说明
- 认证方式：所有需要权限的接口需在请求头添加 `Authorization: Bearer <access_token>`
- 分页参数：统一使用 `page`（默认1）和 `per_page`（默认10）
- 错误响应格式：
  ```json
  {"error":"错误描述"}
  ```
- 成功响应状态码：200（查询/更新）、201（创建）、204（删除）
- 权限控制：通过 `@require_role` 装饰器限制访问角色