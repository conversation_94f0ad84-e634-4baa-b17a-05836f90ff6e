#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API接口测试脚本
测试新开发的字典管理、分类管理、表单模板、权限管理、配置管理API
"""

import requests
import json
import sys
from datetime import datetime

# 配置
BASE_URL = 'http://localhost:5000/api'
TEST_USER = {
    'username': 'admin',
    'password': 'admin123'
}

class APITester:
    def __init__(self):
        self.session = requests.Session()
        self.token = None
        self.test_results = []
    
    def log_test(self, test_name, success, message, response_data=None):
        """记录测试结果"""
        result = {
            'test_name': test_name,
            'success': success,
            'message': message,
            'timestamp': datetime.now().isoformat(),
            'response_data': response_data
        }
        self.test_results.append(result)
        
        status = '✅ PASS' if success else '❌ FAIL'
        print(f"{status} {test_name}: {message}")
        
        if response_data and not success:
            print(f"   Response: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
    
    def login(self):
        """用户登录获取token"""
        try:
            response = self.session.post(
                f"{BASE_URL}/auth/login",
                json=TEST_USER,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                data = response.json()
                self.token = data.get('access_token')
                self.session.headers.update({'Authorization': f'Bearer {self.token}'})
                self.log_test('用户登录', True, '登录成功')
                return True
            else:
                self.log_test('用户登录', False, f'登录失败: {response.status_code}', response.json())
                return False
        except Exception as e:
            self.log_test('用户登录', False, f'登录异常: {str(e)}')
            return False
    
    def test_dictionaries_api(self):
        """测试字典管理API"""
        print("\n=== 测试字典管理API ===")
        
        # 1. 获取字典类型
        try:
            response = self.session.get(f"{BASE_URL}/dictionaries/types")
            if response.status_code == 200:
                data = response.json()
                self.log_test('获取字典类型', True, f'获取到 {len(data.get("types", []))} 个字典类型')
            else:
                self.log_test('获取字典类型', False, f'状态码: {response.status_code}', response.json())
        except Exception as e:
            self.log_test('获取字典类型', False, f'异常: {str(e)}')
        
        # 2. 获取指定类型字典数据
        try:
            response = self.session.get(f"{BASE_URL}/dictionaries/work_order_status")
            if response.status_code == 200:
                data = response.json()
                self.log_test('获取工单状态字典', True, f'获取到 {len(data.get("items", []))} 个字典项')
            else:
                self.log_test('获取工单状态字典', False, f'状态码: {response.status_code}', response.json())
        except Exception as e:
            self.log_test('获取工单状态字典', False, f'异常: {str(e)}')
        
        # 3. 创建字典项（管理员权限）
        try:
            new_dict = {
                'dict_type': 'test_type',
                'dict_key': 'test_key',
                'dict_value': '测试值',
                'dict_label': '测试标签',
                'description': '测试字典项',
                'sort_order': 1
            }
            response = self.session.post(f"{BASE_URL}/dictionaries", json=new_dict)
            if response.status_code == 201:
                self.log_test('创建字典项', True, '字典项创建成功')
            else:
                self.log_test('创建字典项', False, f'状态码: {response.status_code}', response.json())
        except Exception as e:
            self.log_test('创建字典项', False, f'异常: {str(e)}')
    
    def test_material_categories_api(self):
        """测试材料分类管理API"""
        print("\n=== 测试材料分类管理API ===")
        
        # 1. 获取分类树
        try:
            response = self.session.get(f"{BASE_URL}/material_categories/tree")
            if response.status_code == 200:
                data = response.json()
                self.log_test('获取分类树', True, f'获取到 {len(data.get("categories", []))} 个顶级分类')
            else:
                self.log_test('获取分类树', False, f'状态码: {response.status_code}', response.json())
        except Exception as e:
            self.log_test('获取分类树', False, f'异常: {str(e)}')
        
        # 2. 获取扁平化分类列表
        try:
            response = self.session.get(f"{BASE_URL}/material_categories/flat")
            if response.status_code == 200:
                data = response.json()
                self.log_test('获取扁平化分类', True, f'获取到 {len(data.get("categories", []))} 个分类')
            else:
                self.log_test('获取扁平化分类', False, f'状态码: {response.status_code}', response.json())
        except Exception as e:
            self.log_test('获取扁平化分类', False, f'异常: {str(e)}')
        
        # 3. 创建材料分类（管理员权限）
        try:
            import time
            new_category = {
                'name': '测试分类',
                'code': f'TEST_CAT_{int(time.time())}',
                'description': '测试用材料分类',
                'parent_id': None
            }
            response = self.session.post(f"{BASE_URL}/material_categories", json=new_category)
            if response.status_code == 201:
                self.log_test('创建材料分类', True, '材料分类创建成功')
            else:
                self.log_test('创建材料分类', False, f'状态码: {response.status_code}', response.json())
        except Exception as e:
            self.log_test('创建材料分类', False, f'异常: {str(e)}')
    
    def test_form_templates_api(self):
        """测试表单模板API"""
        print("\n=== 测试表单模板API ===")
        
        # 1. 获取表单类型
        try:
            response = self.session.get(f"{BASE_URL}/form_templates/types")
            if response.status_code == 200:
                data = response.json()
                self.log_test('获取表单类型', True, f'获取到 {len(data.get("types", []))} 个表单类型')
            else:
                self.log_test('获取表单类型', False, f'状态码: {response.status_code}', response.json())
        except Exception as e:
            self.log_test('获取表单类型', False, f'异常: {str(e)}')
        
        # 2. 获取工单表单模板
        try:
            response = self.session.get(f"{BASE_URL}/form_templates/work_order")
            if response.status_code == 200:
                data = response.json()
                self.log_test('获取工单表单模板', True, f'获取到 {len(data.get("templates", []))} 个模板字段')
            else:
                self.log_test('获取工单表单模板', False, f'状态码: {response.status_code}', response.json())
        except Exception as e:
            self.log_test('获取工单表单模板', False, f'异常: {str(e)}')
        
        # 3. 创建表单模板（管理员权限）
        try:
            import time
            new_template = {
                'form_type': 'test_form',
                'field_name': f'test_field_{int(time.time())}',
                'field_label': '测试字段',
                'field_type': 'text',
                'is_required': True,
                'sort_order': 1
            }
            response = self.session.post(f"{BASE_URL}/form_templates", json=new_template)
            if response.status_code == 201:
                self.log_test('创建表单模板', True, '表单模板创建成功')
            else:
                self.log_test('创建表单模板', False, f'状态码: {response.status_code}', response.json())
        except Exception as e:
            self.log_test('创建表单模板', False, f'异常: {str(e)}')
    
    def test_permissions_api(self):
        """测试权限管理API"""
        print("\n=== 测试权限管理API ===")
        
        # 1. 获取权限列表
        try:
            response = self.session.get(f"{BASE_URL}/permissions")
            if response.status_code == 200:
                data = response.json()
                self.log_test('获取权限列表', True, f'获取到 {data.get("total", 0)} 个权限')
            else:
                self.log_test('获取权限列表', False, f'状态码: {response.status_code}', response.json())
        except Exception as e:
            self.log_test('获取权限列表', False, f'异常: {str(e)}')
        
        # 2. 获取权限模块
        try:
            response = self.session.get(f"{BASE_URL}/permissions/modules")
            if response.status_code == 200:
                data = response.json()
                self.log_test('获取权限模块', True, f'获取到 {len(data.get("modules", []))} 个模块')
            else:
                self.log_test('获取权限模块', False, f'状态码: {response.status_code}', response.json())
        except Exception as e:
            self.log_test('获取权限模块', False, f'异常: {str(e)}')
        
        # 3. 获取管理员角色权限
        try:
            response = self.session.get(f"{BASE_URL}/permissions/roles/admin/permissions")
            if response.status_code == 200:
                data = response.json()
                self.log_test('获取管理员角色权限', True, f'获取到 {data.get("total", 0)} 个权限')
            else:
                self.log_test('获取管理员角色权限', False, f'状态码: {response.status_code}', response.json())
        except Exception as e:
            self.log_test('获取管理员角色权限', False, f'异常: {str(e)}')
        
        # 4. 检查用户权限
        try:
            check_data = {
                'permission_codes': ['work_order.create', 'material.view']
            }
            response = self.session.post(f"{BASE_URL}/permissions/users/1/check", json=check_data)
            if response.status_code == 200:
                data = response.json()
                self.log_test('检查用户权限', True, '权限检查成功')
            else:
                self.log_test('检查用户权限', False, f'状态码: {response.status_code}', response.json())
        except Exception as e:
            self.log_test('检查用户权限', False, f'异常: {str(e)}')
    
    def test_system_configs_api(self):
        """测试系统配置API"""
        print("\n=== 测试系统配置API ===")
        
        # 1. 获取配置分类
        try:
            response = self.session.get(f"{BASE_URL}/system_configs/categories")
            if response.status_code == 200:
                data = response.json()
                self.log_test('获取配置分类', True, f'获取到 {len(data.get("categories", []))} 个分类')
            else:
                self.log_test('获取配置分类', False, f'状态码: {response.status_code}', response.json())
        except Exception as e:
            self.log_test('获取配置分类', False, f'异常: {str(e)}')
        
        # 2. 获取系统配置列表
        try:
            response = self.session.get(f"{BASE_URL}/system_configs?category=system")
            if response.status_code == 200:
                data = response.json()
                self.log_test('获取系统配置', True, f'获取到 {data.get("total", 0)} 个配置项')
            else:
                self.log_test('获取系统配置', False, f'状态码: {response.status_code}', response.json())
        except Exception as e:
            self.log_test('获取系统配置', False, f'异常: {str(e)}')
        
        # 3. 根据键获取配置
        try:
            response = self.session.get(f"{BASE_URL}/system_configs/key/system.name")
            if response.status_code == 200:
                data = response.json()
                self.log_test('根据键获取配置', True, f'获取配置值: {data.get("config_value")}')
            else:
                self.log_test('根据键获取配置', False, f'状态码: {response.status_code}', response.json())
        except Exception as e:
            self.log_test('根据键获取配置', False, f'异常: {str(e)}')
        
        # 4. 批量获取配置
        try:
            batch_data = {
                'config_keys': ['system.name', 'system.version', 'work_order.auto_assign']
            }
            response = self.session.post(f"{BASE_URL}/system_configs/batch", json=batch_data)
            if response.status_code == 200:
                data = response.json()
                self.log_test('批量获取配置', True, f'获取到 {len(data.get("configs", {}))} 个配置')
            else:
                self.log_test('批量获取配置', False, f'状态码: {response.status_code}', response.json())
        except Exception as e:
            self.log_test('批量获取配置', False, f'异常: {str(e)}')
        
        # 5. 创建配置项（管理员权限）
        try:
            import time
            new_config = {
                'config_key': f'test.config_{int(time.time())}',
                'config_value': 'test_value',
                'category': 'test',
                'name': '测试配置',
                'description': '测试配置项',
                'data_type': 'string',
                'is_public': True
            }
            response = self.session.post(f"{BASE_URL}/system_configs", json=new_config)
            if response.status_code == 201:
                self.log_test('创建配置项', True, '配置项创建成功')
            else:
                self.log_test('创建配置项', False, f'状态码: {response.status_code}', response.json())
        except Exception as e:
            self.log_test('创建配置项', False, f'异常: {str(e)}')
    
    def test_error_handling(self):
        """测试错误处理"""
        print("\n=== 测试错误处理 ===")
        
        # 1. 测试无效token
        old_token = self.session.headers.get('Authorization')
        self.session.headers.update({'Authorization': 'Bearer invalid_token'})
        
        try:
            response = self.session.get(f"{BASE_URL}/permissions")
            if response.status_code == 401:
                self.log_test('无效token测试', True, '正确返回401错误')
            else:
                self.log_test('无效token测试', False, f'期望401，实际: {response.status_code}')
        except Exception as e:
            self.log_test('无效token测试', False, f'异常: {str(e)}')
        
        # 恢复token
        self.session.headers.update({'Authorization': old_token})
        
        # 2. 测试不存在的资源
        try:
            response = self.session.get(f"{BASE_URL}/dictionaries/nonexistent_type")
            if response.status_code == 404:
                self.log_test('不存在资源测试', True, '正确返回404错误')
            else:
                self.log_test('不存在资源测试', False, f'期望404，实际: {response.status_code}')
        except Exception as e:
            self.log_test('不存在资源测试', False, f'异常: {str(e)}')
        
        # 3. 测试无效参数
        try:
            invalid_data = {'invalid_field': 'value'}
            response = self.session.post(f"{BASE_URL}/dictionaries", json=invalid_data)
            if response.status_code == 400:
                self.log_test('无效参数测试', True, '正确返回400错误')
            else:
                self.log_test('无效参数测试', False, f'期望400，实际: {response.status_code}')
        except Exception as e:
            self.log_test('无效参数测试', False, f'异常: {str(e)}')
    
    def generate_report(self):
        """生成测试报告"""
        print("\n" + "="*50)
        print("API测试报告")
        print("="*50)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests}")
        print(f"失败: {failed_tests}")
        print(f"成功率: {(passed_tests/total_tests*100):.1f}%")
        
        if failed_tests > 0:
            print("\n失败的测试:")
            for result in self.test_results:
                if not result['success']:
                    print(f"  - {result['test_name']}: {result['message']}")
        
        # 保存详细报告到文件
        report_file = f"api_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump({
                'summary': {
                    'total_tests': total_tests,
                    'passed_tests': passed_tests,
                    'failed_tests': failed_tests,
                    'success_rate': passed_tests/total_tests*100
                },
                'test_results': self.test_results
            }, f, indent=2, ensure_ascii=False)
        
        print(f"\n详细报告已保存到: {report_file}")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("开始API接口测试...")
        print(f"测试目标: {BASE_URL}")
        
        # 登录
        if not self.login():
            print("登录失败，无法继续测试")
            return
        
        # 运行各模块测试
        self.test_dictionaries_api()
        self.test_material_categories_api()
        self.test_form_templates_api()
        self.test_permissions_api()
        self.test_system_configs_api()
        self.test_error_handling()
        
        # 生成报告
        self.generate_report()

def main():
    """主函数"""
    if len(sys.argv) > 1:
        global BASE_URL
        BASE_URL = sys.argv[1]
    
    tester = APITester()
    tester.run_all_tests()

if __name__ == '__main__':
    main()