# 调度系统后端API详细说明文档

## 概述

本文档详细描述了调度系统后端的所有API接口，包括认证、用户管理、工单管理、材料管理、车辆管理等核心功能模块。

### 基础信息
- **基础URL**: `http://localhost:5000/api`
- **认证方式**: JWT Bearer Token
- **数据格式**: JSON
- **字符编码**: UTF-8

### 通用响应格式
```json
{
  "data": {},           // 成功时的数据
  "error": "string",    // 错误时的错误信息
  "message": "string"   // 操作结果消息
}
```

### 通用状态码
- `200`: 成功
- `201`: 创建成功
- `400`: 请求参数错误
- `401`: 未授权/认证失败
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

---

## 1. 认证模块 (/api/auth)

### 1.1 用户登录
- **路径**: `POST /auth/login`
- **功能**: 用户登录并获取访问令牌
- **权限**: 无需认证
- **请求体**:
```json
{
  "username": "admin",
  "password": "admin123"
}
```
- **响应示例**:
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": 1,
    "username": "admin",
    "name": "系统管理员",
    "email": "<EMAIL>",
    "role": "admin",
    "is_active": true,
    "created_at": "2023-01-01T00:00:00"
  }
}
```

### 1.2 获取当前用户信息
- **路径**: `GET /auth/profile`
- **功能**: 获取当前登录用户的详细信息
- **权限**: 需要JWT认证
- **响应示例**:
```json
{
  "user": {
    "id": 1,
    "username": "admin",
    "name": "系统管理员",
    "email": "<EMAIL>",
    "role": "admin",
    "group_id": null,
    "is_active": true,
    "created_at": "2023-01-01T00:00:00"
  }
}
```

---

## 2. 用户管理模块 (/api/users)

### 2.1 获取用户列表
- **路径**: `GET /users`
- **功能**: 分页获取用户列表，支持搜索
- **权限**: `user.view`
- **查询参数**:
  - `page`: 页码 (默认: 1)
  - `per_page`: 每页条数 (默认: 10)
  - `search`: 搜索关键词 (可选)
- **响应示例**:
```json
{
  "users": [
    {
      "id": 1,
      "username": "admin",
      "name": "系统管理员",
      "email": "<EMAIL>",
      "role": "admin",
      "is_active": true
    }
  ],
  "total": 10,
  "pages": 1,
  "current_page": 1
}
```

### 2.2 创建用户
- **路径**: `POST /users`
- **功能**: 创建新用户
- **权限**: `user.create`
- **请求体**:
```json
{
  "username": "newuser",
  "email": "<EMAIL>",
  "password": "password123",
  "name": "新用户",
  "employee_id": "E001",
  "phone": "13800138000",
  "position": "工程师",
  "department": "技术部",
  "role": "user",
  "id_card": "110101199001010001",
  "ethnicity": "汉族",
  "gender": "男",
  "hire_date": "2023-01-01",
  "is_driver": false,
  "group_id": 1
}
```

### 2.3 更新用户
- **路径**: `PUT /users/{user_id}`
- **功能**: 更新用户信息
- **权限**: `user.edit`
- **请求体**: 同创建用户，字段可选

### 2.4 删除用户
- **路径**: `DELETE /users/{user_id}`
- **功能**: 删除用户
- **权限**: `user.delete`

### 2.5 重置用户密码
- **路径**: `POST /users/{user_id}/reset-password`
- **功能**: 重置用户密码
- **权限**: `user.edit`
- **请求体**:
```json
{
  "new_password": "newpassword123"
}
```

### 2.6 获取用户区域权限
- **路径**: `GET /users/{user_id}/areas`
- **功能**: 获取用户的区域权限列表
- **权限**: `user.view`

### 2.7 设置用户区域权限
- **路径**: `POST /users/{user_id}/areas`
- **功能**: 设置用户的区域权限
- **权限**: `user.edit`
- **请求体**:
```json
{
  "area_ids": [1, 2, 3]
}
```

---

## 3. 分组管理模块 (/api/groups)

### 3.1 获取分组列表
- **路径**: `GET /groups`
- **功能**: 分页获取分组列表
- **权限**: `group.view`
- **查询参数**:
  - `page`: 页码 (默认: 1)
  - `per_page`: 每页条数 (默认: 10)
  - `search`: 搜索关键词 (可选)

### 3.2 创建分组
- **路径**: `POST /groups`
- **功能**: 创建新分组
- **权限**: `group.create`
- **请求体**:
```json
{
  "name": "维修一组",
  "description": "负责A区域的维修任务"
}
```

### 3.3 更新分组
- **路径**: `PUT /groups/{group_id}`
- **功能**: 更新分组信息
- **权限**: `group.edit`

### 3.4 删除分组
- **路径**: `DELETE /groups/{group_id}`
- **功能**: 删除分组
- **权限**: `group.delete`

---

## 4. 区域管理模块 (/api/areas)

### 4.1 获取区域列表
- **路径**: `GET /areas`
- **功能**: 分页获取区域列表
- **权限**: `area.view`
- **查询参数**:
  - `page`: 页码 (默认: 1)
  - `per_page`: 每页条数 (默认: 10)
  - `search`: 搜索关键词 (可选)
  - `include_members`: 是否包含成员信息 (默认: true)

### 4.2 获取区域详情
- **路径**: `GET /areas/{area_id}`
- **功能**: 获取单个区域详情
- **权限**: `area.view`
- **查询参数**:
  - `include_members`: 是否包含成员信息 (默认: true)

### 4.3 创建区域
- **路径**: `POST /areas`
- **功能**: 创建新区域
- **权限**: `area.create`

### 4.4 更新区域
- **路径**: `PUT /areas/{area_id}`
- **功能**: 更新区域信息
- **权限**: `area.edit`

### 4.5 删除区域
- **路径**: `DELETE /areas/{area_id}`
- **功能**: 删除区域
- **权限**: `area.delete`

---

## 5. 车辆管理模块 (/api/vehicles)

### 5.1 获取车辆列表
- **路径**: `GET /vehicles`
- **功能**: 分页获取车辆列表
- **权限**: `vehicle.view`
- **查询参数**:
  - `page`: 页码 (默认: 1)
  - `per_page`: 每页条数 (默认: 10)
  - `search`: 搜索关键词 (可选)
  - `status`: 车辆状态 (可选)
  - `group_id`: 分组ID (可选)

### 5.2 创建车辆
- **路径**: `POST /vehicles`
- **功能**: 创建新车辆
- **权限**: `vehicle.create`
- **请求体**:
```json
{
  "license_plate": "京A88888",
  "brand": "福特",
  "model": "全顺",
  "year": 2023,
  "vehicle_type": "工程车",
  "capacity": "2吨",
  "fuel_type": "汽油",
  "status": "available",
  "group_id": 1
}
```

### 5.3 更新车辆
- **路径**: `PUT /vehicles/{vehicle_id}`
- **功能**: 更新车辆信息
- **权限**: `vehicle.edit`

### 5.4 删除车辆
- **路径**: `DELETE /vehicles/{vehicle_id}`
- **功能**: 删除车辆
- **权限**: `vehicle.delete`

---

## 6. 材料管理模块 (/api/materials)

### 6.1 获取材料列表
- **路径**: `GET /materials`
- **功能**: 分页获取材料列表
- **权限**: `material.view`
- **查询参数**:
  - `page`: 页码 (默认: 1)
  - `per_page`: 每页条数 (默认: 10)
  - `search`: 搜索关键词 (可选)
  - `category`: 材料分类 (可选)
  - `low_stock`: 是否只显示低库存 (可选)

### 6.2 创建材料
- **路径**: `POST /materials`
- **功能**: 创建新材料
- **权限**: `material.create`
- **请求体**:
```json
{
  "name": "螺丝刀套装",
  "code": "M001",
  "category": "工具",
  "unit": "套",
  "specification": "多规格螺丝刀",
  "stock_quantity": 10,
  "min_stock": 2,
  "unit_price": 50.00,
  "supplier": "工具供应商"
}
```

### 6.3 更新材料
- **路径**: `PUT /materials/{material_id}`
- **功能**: 更新材料信息
- **权限**: `material.edit`

### 6.4 删除材料
- **路径**: `DELETE /materials/{material_id}`
- **功能**: 删除材料
- **权限**: `material.delete`

### 6.5 获取材料分类列表
- **路径**: `GET /materials/categories`
- **功能**: 获取所有材料分类
- **权限**: `material.view`

### 6.6 更新材料库存
- **路径**: `POST /materials/{material_id}/stock`
- **功能**: 更新材料库存
- **权限**: `material.edit`
- **请求体**:
```json
{
  "operation": "in",  // "in" 入库, "out" 出库
  "quantity": 10,
  "notes": "采购入库"
}
```
