<template>
  <el-card>
    <el-table :data="users" style="width: 100%" v-loading="loading">
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="username" label="用户名" width="120" />
      <el-table-column prop="name" label="姓名" width="100" />
      <el-table-column prop="employee_id" label="工号" width="120" />
      <el-table-column prop="phone" label="电话" width="130" />
      <el-table-column prop="position" label="职位" width="120" />
      <el-table-column prop="department" label="部门" width="120" />
      <el-table-column label="区域" width="150">
        <template #default="scope">
          <span v-if="scope.row.areas && scope.row.areas.length > 0">
            {{ scope.row.areas.join(', ') }}
          </span>
          <span v-else-if="scope.row.area_name">
            {{ scope.row.area_name }}
          </span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="gender" label="性别" width="80">
        <template #default="scope">
          {{ scope.row.gender === 'male' ? '男' : scope.row.gender === 'female' ? '女' : scope.row.gender }}
        </template>
      </el-table-column>
      <el-table-column prop="ethnicity" label="民族" width="100" />
      <el-table-column prop="hire_date" label="入职时间" width="120" />
      <el-table-column prop="role" label="角色" width="100">
        <template #default="scope">
          {{ getRoleLabel(scope.row.role) }}
        </template>
      </el-table-column>
      <el-table-column prop="is_driver" label="是否司机" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.is_driver ? 'success' : 'info'">
            {{ scope.row.is_driver ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.status === 'active' ? 'success' : 'danger'">
            {{ scope.row.status === 'active' ? '在职' : '离职' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="scope">
          <el-button size="small" @click="$emit('edit', scope.row)">编辑</el-button>
          <el-button size="small" @click="$emit('manage-areas', scope.row)">管理区域</el-button>
          <el-button 
            size="small" 
            type="danger" 
            @click="$emit('delete', scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </el-card>
</template>

<script setup lang="ts">
interface User {
  id: number
  username: string
  name: string
  employee_id: string
  phone: string
  position: string
  department: string
  areas?: string[]
  area_name?: string
  gender: string
  ethnicity: string
  hire_date: string
  role: string
  is_driver: boolean
  status: string
}

interface Props {
  users: User[]
  loading: boolean
  roleOptions: Array<{ label: string; value: string }>
}

defineProps<Props>()

defineEmits<{
  edit: [user: User]
  'manage-areas': [user: User]
  delete: [user: User]
}>()

// 获取角色标签的辅助函数
const getRoleLabel = (roleValue: string) => {
  const props = defineProps<Props>()
  const role = props.roleOptions.find(r => r.value === roleValue)
  return role ? role.label : roleValue
}
</script>

<style scoped>
.el-card {
  margin-top: 20px;
}
</style>
