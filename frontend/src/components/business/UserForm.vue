<template>
  <el-dialog
    :model-value="visible"
    :title="editingUser ? '编辑用户' : '添加用户'"
    width="800px"
    @update:model-value="$emit('update:visible', $event)"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="formRules"
      label-width="120px"
      class="user-form"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="用户名" prop="username">
            <el-input v-model="form.username" placeholder="请输入用户名" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="姓名" prop="name">
            <el-input v-model="form.name" placeholder="请输入姓名" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="form.email" placeholder="请输入邮箱" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="工号" prop="employee_id">
            <el-input v-model="form.employee_id" placeholder="请输入工号" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="电话" prop="phone">
            <el-input v-model="form.phone" placeholder="请输入电话号码" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="职位" prop="position">
            <el-input v-model="form.position" placeholder="请输入职位" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="部门" prop="department">
            <el-input v-model="form.department" placeholder="请输入部门" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="角色" prop="role">
            <el-select v-model="form.role" placeholder="请选择角色" style="width: 100%">
              <el-option
                v-for="role in roleOptions"
                :key="role.value"
                :label="role.label"
                :value="role.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="身份证号" prop="id_card">
            <el-input v-model="form.id_card" placeholder="请输入身份证号" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="民族" prop="ethnicity">
            <el-input v-model="form.ethnicity" placeholder="请输入民族" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="性别" prop="gender">
            <el-select v-model="form.gender" placeholder="请选择性别" style="width: 100%">
              <el-option label="男" value="male" />
              <el-option label="女" value="female" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="入职时间" prop="hire_date">
            <el-date-picker
              v-model="form.hire_date"
              type="date"
              placeholder="请选择入职时间"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="是否司机" prop="is_driver">
            <el-switch v-model="form.is_driver" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-select v-model="form.status" placeholder="请选择状态" style="width: 100%">
              <el-option label="在职" value="active" />
              <el-option label="离职" value="inactive" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="区域权限" prop="area">
            <el-select
              v-model="form.area"
              multiple
              placeholder="请选择区域权限"
              style="width: 100%"
            >
              <el-option
                v-for="area in areaOptions"
                :key="area.id"
                :label="area.name"
                :value="area.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="!editingUser">
          <el-form-item label="密码" prop="password">
            <el-input
              v-model="form.password"
              type="password"
              placeholder="请输入密码"
              show-password
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="备注" prop="remarks">
        <el-input
          v-model="form.remarks"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitting">
        {{ editingUser ? '更新' : '创建' }}
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, type FormInstance } from 'vue'
import { ElMessage } from 'element-plus'

interface User {
  id?: number
  username: string
  name: string
  email: string
  employee_id: string
  phone: string
  position: string
  department: string
  role: string
  id_card: string
  ethnicity: string
  gender: string
  hire_date: string
  is_driver: boolean
  status: string
  area: number[]
  password?: string
  remarks: string
}

interface Props {
  visible: boolean
  editingUser?: User | null
  roleOptions: Array<{ label: string; value: string }>
  areaOptions: Array<{ id: number; name: string }>
  submitting?: boolean
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:visible': [value: boolean]
  submit: [formData: User]
}>()

const formRef = ref<FormInstance>()

const form = reactive<User>({
  username: '',
  name: '',
  email: '',
  employee_id: '',
  phone: '',
  position: '',
  department: '',
  role: 'user',
  id_card: '',
  ethnicity: '',
  gender: '',
  hire_date: '',
  is_driver: false,
  status: 'active',
  area: [],
  password: '',
  remarks: ''
})

const formRules = {
  username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  employee_id: [{ required: true, message: '请输入工号', trigger: 'blur' }],
  phone: [{ required: true, message: '请输入电话号码', trigger: 'blur' }],
  position: [{ required: true, message: '请输入职位', trigger: 'blur' }],
  department: [{ required: true, message: '请输入部门', trigger: 'blur' }],
  role: [{ required: true, message: '请选择角色', trigger: 'change' }],
  id_card: [{ required: true, message: '请输入身份证号', trigger: 'blur' }],
  ethnicity: [{ required: true, message: '请输入民族', trigger: 'blur' }],
  gender: [{ required: true, message: '请选择性别', trigger: 'change' }],
  hire_date: [{ required: true, message: '请选择入职时间', trigger: 'change' }],
  password: props.editingUser ? [] : [{ required: true, message: '请输入密码', trigger: 'blur' }]
}

// 监听编辑用户变化，填充表单
watch(() => props.editingUser, (user) => {
  if (user) {
    Object.assign(form, {
      ...user,
      area: user.area || []
    })
  } else {
    resetForm()
  }
}, { immediate: true })

const resetForm = () => {
  Object.assign(form, {
    username: '',
    name: '',
    email: '',
    employee_id: '',
    phone: '',
    position: '',
    department: '',
    role: 'user',
    id_card: '',
    ethnicity: '',
    gender: '',
    hire_date: '',
    is_driver: false,
    status: 'active',
    area: [],
    password: '',
    remarks: ''
  })
  formRef.value?.clearValidate()
}

const handleClose = () => {
  emit('update:visible', false)
  resetForm()
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    emit('submit', { ...form })
  } catch (error) {
    ElMessage.error('请检查表单填写是否正确')
  }
}
</script>

<style scoped>
.user-form {
  max-height: 60vh;
  overflow-y: auto;
}
</style>
