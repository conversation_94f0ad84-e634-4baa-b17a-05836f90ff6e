<template>
  <el-dialog
    :model-value="visible"
    title="导入规则说明"
    width="600px"
    @update:model-value="$emit('update:visible', $event)"
  >
    <div class="import-rules">
      <h3>CSV文件格式要求：</h3>
      <ol>
        <li>文件必须是UTF-8编码的CSV格式</li>
        <li>第一行必须是列标题（表头）</li>
        <li>必填字段：用户名、姓名、邮箱、工号、电话、职位、部门、身份证号、民族、性别、入职时间</li>
        <li>可选字段：区域权限、备注、是否司机等</li>
      </ol>
      
      <h3>列标题对应关系：</h3>
      <el-table :data="columnMapping" size="small" border>
        <el-table-column prop="field" label="字段名" width="120" />
        <el-table-column prop="header" label="CSV列标题" width="150" />
        <el-table-column prop="required" label="是否必填" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.required ? 'danger' : 'info'">
              {{ scope.row.required ? '必填' : '可选' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="example" label="示例值" />
      </el-table>
      
      <h3>注意事项：</h3>
      <ul>
        <li>用户名必须唯一，不能与现有用户重复</li>
        <li>邮箱格式必须正确</li>
        <li>身份证号必须是18位有效身份证号</li>
        <li>性别只能填写"男"或"女"</li>
        <li>角色可选值：admin（管理员）、manager（经理）、user（普通用户）</li>
        <li>入职时间格式：YYYY-MM-DD</li>
        <li>是否司机：是/否 或 true/false</li>
      </ul>
      
      <h3>示例CSV内容：</h3>
      <el-input
        type="textarea"
        :rows="6"
        readonly
        :value="csvExample"
        class="csv-example"
      />
    </div>
    
    <template #footer>
      <el-button @click="$emit('update:visible', false)">关闭</el-button>
      <el-button type="primary" @click="downloadTemplate">下载模板</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus'

defineProps<{
  visible: boolean
}>()

defineEmits<{
  'update:visible': [value: boolean]
}>()

const columnMapping = [
  { field: '用户名', header: 'username', required: true, example: 'zhangsan' },
  { field: '姓名', header: 'name', required: true, example: '张三' },
  { field: '邮箱', header: 'email', required: true, example: '<EMAIL>' },
  { field: '工号', header: 'employee_id', required: true, example: 'E001' },
  { field: '电话', header: 'phone', required: true, example: '13800138000' },
  { field: '职位', header: 'position', required: true, example: '工程师' },
  { field: '部门', header: 'department', required: true, example: '技术部' },
  { field: '角色', header: 'role', required: false, example: 'user' },
  { field: '身份证号', header: 'id_card', required: true, example: '110101199001010001' },
  { field: '民族', header: 'ethnicity', required: true, example: '汉族' },
  { field: '性别', header: 'gender', required: true, example: '男' },
  { field: '入职时间', header: 'hire_date', required: true, example: '2023-01-01' },
  { field: '是否司机', header: 'is_driver', required: false, example: '否' },
  { field: '备注', header: 'remarks', required: false, example: '备注信息' }
]

const csvExample = `username,name,email,employee_id,phone,position,department,role,id_card,ethnicity,gender,hire_date,is_driver,remarks
zhangsan,张三,<EMAIL>,E001,13800138000,工程师,技术部,user,110101199001010001,汉族,男,2023-01-01,否,技术骨干
lisi,李四,<EMAIL>,E002,13800138001,经理,管理部,manager,110101199002020002,汉族,女,2023-01-02,是,部门经理`

const downloadTemplate = () => {
  const headers = columnMapping.map(item => item.header).join(',')
  const examples = columnMapping.map(item => item.example).join(',')
  const csvContent = `${headers}\n${examples}`
  
  const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)
  
  link.setAttribute('href', url)
  link.setAttribute('download', '用户导入模板.csv')
  link.style.visibility = 'hidden'
  
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  
  ElMessage.success('模板下载成功')
}
</script>

<style scoped>
.import-rules {
  max-height: 60vh;
  overflow-y: auto;
}

.import-rules h3 {
  color: #409eff;
  margin-top: 20px;
  margin-bottom: 10px;
}

.import-rules ol,
.import-rules ul {
  padding-left: 20px;
}

.import-rules li {
  margin-bottom: 5px;
  line-height: 1.5;
}

.csv-example {
  margin-top: 10px;
}

.csv-example :deep(.el-textarea__inner) {
  font-family: 'Courier New', monospace;
  font-size: 12px;
}
</style>
