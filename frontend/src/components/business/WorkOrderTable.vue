<template>
  <el-card>
    <el-table :data="workOrders" style="width: 100%" v-loading="loading">
      <el-table-column prop="id" label="工单号" width="120" />
      <el-table-column prop="status" label="状态" width="100">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.status)">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="title" label="标题" min-width="200" />
      <el-table-column prop="priority" label="优先级" width="100">
        <template #default="scope">
          <el-tag :type="getPriorityType(scope.row.priority)">
            {{ getPriorityText(scope.row.priority) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="area_name" label="区域" width="120" />
      <el-table-column prop="assignee_name" label="负责人" width="120" />
      <el-table-column prop="creator_name" label="创建人" width="120" />
      <el-table-column prop="created_at" label="创建时间" width="180">
        <template #default="scope">
          {{ formatDateTime(scope.row.created_at) }}
        </template>
      </el-table-column>
      <el-table-column prop="due_date" label="截止时间" width="180">
        <template #default="scope">
          <span v-if="scope.row.due_date" :class="getDueDateClass(scope.row.due_date, scope.row.status)">
            {{ formatDateTime(scope.row.due_date) }}
          </span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="scope">
          <el-button size="small" @click="$emit('view', scope.row)">
            查看
          </el-button>
          <el-button 
            size="small" 
            type="primary" 
            @click="$emit('edit', scope.row)"
            v-if="canEdit(scope.row)"
          >
            编辑
          </el-button>
          <el-button 
            size="small" 
            type="success" 
            @click="$emit('complete', scope.row)"
            v-if="canComplete(scope.row)"
          >
            完成
          </el-button>
          <el-button 
            size="small" 
            type="danger" 
            @click="$emit('delete', scope.row)"
            v-if="canDelete(scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页 -->
    <div class="pagination-container" v-if="total > 0">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface WorkOrder {
  id: number
  title: string
  status: string
  priority: string
  area_name: string
  assignee_name: string
  creator_name: string
  created_at: string
  due_date?: string
}

interface Props {
  workOrders: WorkOrder[]
  loading: boolean
  total: number
  currentPage: number
  pageSize: number
  userRole?: string
  userId?: number
}

const props = defineProps<Props>()

const emit = defineEmits<{
  view: [workOrder: WorkOrder]
  edit: [workOrder: WorkOrder]
  complete: [workOrder: WorkOrder]
  delete: [workOrder: WorkOrder]
  'size-change': [size: number]
  'current-change': [page: number]
}>()

// 状态相关函数
const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    'pending': 'warning',
    'assigned': 'info',
    'in_progress': 'primary',
    'completed': 'success',
    'cancelled': 'danger',
    '未分配': 'warning',
    '待处理': 'warning',
    '处理中': 'primary',
    '已完成': 'success'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'pending': '待处理',
    'assigned': '已分配',
    'in_progress': '处理中',
    'completed': '已完成',
    'cancelled': '已取消',
    '未分配': '未分配',
    '待处理': '待处理',
    '处理中': '处理中',
    '已完成': '已完成'
  }
  return statusMap[status] || status
}

const getPriorityType = (priority: string) => {
  const priorityMap: Record<string, string> = {
    'low': 'info',
    'medium': 'warning',
    'high': 'danger',
    'urgent': 'danger'
  }
  return priorityMap[priority] || 'info'
}

const getPriorityText = (priority: string) => {
  const priorityMap: Record<string, string> = {
    'low': '低',
    'medium': '中',
    'high': '高',
    'urgent': '紧急'
  }
  return priorityMap[priority] || priority
}

// 权限检查函数
const canEdit = (workOrder: WorkOrder) => {
  if (props.userRole === 'admin') return true
  if (workOrder.status === 'completed' || workOrder.status === '已完成') return false
  return true
}

const canComplete = (workOrder: WorkOrder) => {
  if (workOrder.status === 'completed' || workOrder.status === '已完成') return false
  return true
}

const canDelete = (workOrder: WorkOrder) => {
  if (props.userRole === 'admin') return true
  if (workOrder.status === 'completed' || workOrder.status === '已完成') return false
  return true
}

// 时间格式化
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 截止时间样式
const getDueDateClass = (dueDate: string, status: string) => {
  if (status === 'completed' || status === '已完成') return ''
  
  const now = new Date()
  const due = new Date(dueDate)
  const diffHours = (due.getTime() - now.getTime()) / (1000 * 60 * 60)
  
  if (diffHours < 0) return 'overdue'
  if (diffHours < 24) return 'due-soon'
  return ''
}

// 分页事件处理
const handleSizeChange = (size: number) => {
  emit('size-change', size)
}

const handleCurrentChange = (page: number) => {
  emit('current-change', page)
}
</script>

<style scoped>
.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.overdue {
  color: #f56c6c;
  font-weight: bold;
}

.due-soon {
  color: #e6a23c;
  font-weight: bold;
}

@media (max-width: 768px) {
  .pagination-container {
    text-align: center;
  }
  
  .pagination-container :deep(.el-pagination) {
    justify-content: center;
  }
}
</style>
