<template>
  <el-dialog
    :model-value="visible"
    :title="`管理用户区域权限 - ${user?.name}`"
    width="500px"
    @update:model-value="$emit('update:visible', $event)"
    @close="handleClose"
  >
    <div v-if="user" class="area-management">
      <div class="user-info">
        <p><strong>用户名：</strong>{{ user.username }}</p>
        <p><strong>姓名：</strong>{{ user.name }}</p>
        <p><strong>部门：</strong>{{ user.department }}</p>
      </div>
      
      <el-divider />
      
      <div class="area-selection">
        <h4>选择区域权限：</h4>
        <el-checkbox-group v-model="selectedAreas" class="area-checkboxes">
          <el-checkbox
            v-for="area in areaOptions"
            :key="area.id"
            :label="area.id"
            :value="area.id"
          >
            {{ area.name }}
          </el-checkbox>
        </el-checkbox-group>
      </div>
      
      <div class="current-areas" v-if="user.areas && user.areas.length > 0">
        <h4>当前区域权限：</h4>
        <el-tag
          v-for="areaName in user.areas"
          :key="areaName"
          type="success"
          class="area-tag"
        >
          {{ areaName }}
        </el-tag>
      </div>
    </div>
    
    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitting">
        保存
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'

interface User {
  id: number
  username: string
  name: string
  department: string
  areas?: string[]
}

interface Area {
  id: number
  name: string
}

interface Props {
  visible: boolean
  user?: User | null
  areaOptions: Area[]
  submitting?: boolean
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:visible': [value: boolean]
  submit: [userId: number, areaIds: number[]]
}>()

const selectedAreas = ref<number[]>([])

// 监听用户变化，加载用户当前的区域权限
watch(() => props.user, async (user) => {
  if (user) {
    try {
      // 这里应该调用API获取用户的区域权限
      // 暂时使用模拟数据
      selectedAreas.value = []
    } catch (error) {
      ElMessage.error('加载用户区域权限失败')
    }
  }
}, { immediate: true })

const handleClose = () => {
  emit('update:visible', false)
  selectedAreas.value = []
}

const handleSubmit = () => {
  if (!props.user) return
  
  emit('submit', props.user.id, selectedAreas.value)
}
</script>

<style scoped>
.area-management {
  padding: 10px 0;
}

.user-info p {
  margin: 8px 0;
  color: #606266;
}

.area-selection h4,
.current-areas h4 {
  margin: 15px 0 10px 0;
  color: #409eff;
  font-size: 14px;
}

.area-checkboxes {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.area-checkboxes :deep(.el-checkbox) {
  margin-right: 0;
  margin-bottom: 8px;
}

.current-areas {
  margin-top: 20px;
}

.area-tag {
  margin-right: 8px;
  margin-bottom: 8px;
}
</style>
