<template>
  <el-card class="search-card">
    <el-form :model="searchForm" :inline="true" class="search-form">
      <el-form-item label="搜索">
        <el-input
          v-model="searchForm.keyword"
          placeholder="请输入关键词搜索"
          clearable
          @keyup.enter="handleSearch"
          style="width: 200px"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </el-form-item>
      
      <el-form-item v-if="showRoleFilter" label="角色">
        <el-select
          v-model="searchForm.role"
          placeholder="请选择角色"
          clearable
          style="width: 120px"
        >
          <el-option
            v-for="role in roleOptions"
            :key="role.value"
            :label="role.label"
            :value="role.value"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item v-if="showStatusFilter" label="状态">
        <el-select
          v-model="searchForm.status"
          placeholder="请选择状态"
          clearable
          style="width: 120px"
        >
          <el-option label="在职" value="active" />
          <el-option label="离职" value="inactive" />
        </el-select>
      </el-form-item>
      
      <el-form-item v-if="showAreaFilter" label="区域">
        <el-select
          v-model="searchForm.area"
          placeholder="请选择区域"
          clearable
          style="width: 150px"
        >
          <el-option
            v-for="area in areaOptions"
            :key="area.id"
            :label="area.name"
            :value="area.id"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item>
        <el-button type="primary" @click="handleSearch" :icon="Search">
          搜索
        </el-button>
        <el-button @click="handleReset" :icon="Refresh">
          重置
        </el-button>
      </el-form-item>
    </el-form>
  </el-card>
</template>

<script setup lang="ts">
import { reactive } from 'vue'
import { Search, Refresh } from '@element-plus/icons-vue'

interface SearchFormData {
  keyword: string
  role?: string
  status?: string
  area?: number
}

interface Props {
  showRoleFilter?: boolean
  showStatusFilter?: boolean
  showAreaFilter?: boolean
  roleOptions?: Array<{ label: string; value: string }>
  areaOptions?: Array<{ id: number; name: string }>
}

const props = withDefaults(defineProps<Props>(), {
  showRoleFilter: false,
  showStatusFilter: false,
  showAreaFilter: false,
  roleOptions: () => [],
  areaOptions: () => []
})

const emit = defineEmits<{
  search: [params: SearchFormData]
  reset: []
}>()

const searchForm = reactive<SearchFormData>({
  keyword: '',
  role: '',
  status: '',
  area: undefined
})

const handleSearch = () => {
  const params: SearchFormData = {
    keyword: searchForm.keyword
  }
  
  if (props.showRoleFilter && searchForm.role) {
    params.role = searchForm.role
  }
  
  if (props.showStatusFilter && searchForm.status) {
    params.status = searchForm.status
  }
  
  if (props.showAreaFilter && searchForm.area) {
    params.area = searchForm.area
  }
  
  emit('search', params)
}

const handleReset = () => {
  searchForm.keyword = ''
  searchForm.role = ''
  searchForm.status = ''
  searchForm.area = undefined
  
  emit('reset')
}
</script>

<style scoped>
.search-card {
  margin-bottom: 20px;
}

.search-form {
  margin: 0;
}

.search-form :deep(.el-form-item) {
  margin-bottom: 0;
  margin-right: 16px;
}

.search-form :deep(.el-form-item:last-child) {
  margin-right: 0;
}

@media (max-width: 768px) {
  .search-form {
    display: block;
  }
  
  .search-form :deep(.el-form-item) {
    display: block;
    margin-bottom: 12px;
    margin-right: 0;
  }
  
  .search-form :deep(.el-form-item:last-child) {
    margin-bottom: 0;
  }
}
</style>
