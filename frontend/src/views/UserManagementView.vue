<template>
  <div class="user-management page-container">
    <PageHeader title="用户管理" description="管理系统用户信息和权限">
      <template #actions>
        <el-button type="primary" :icon="Plus" @click="showAddDialog = true">
          添加用户
        </el-button>
        <el-button type="info" @click="exportUsers">
          导出用户
        </el-button>
        <el-upload
          class="upload-btn"
          :show-file-list="false"
          :before-upload="beforeUpload"
          :on-change="handleImport"
          accept=".csv"
        >
          <el-button type="warning">导入用户</el-button>
        </el-upload>
        <el-button type="info" @click="showImportRules = true">导入规则</el-button>
      </template>
    </PageHeader>

    <!-- 搜索表单 -->
    <SearchForm
      :show-role-filter="true"
      :show-status-filter="true"
      :show-area-filter="true"
      :role-options="roleOptions"
      :area-options="areaOptions"
      @search="handleSearch"
      @reset="handleReset"
    />

    <!-- 用户表格 -->
    <UserTable
      :users="users"
      :loading="loading"
      :role-options="roleOptions"
      @edit="editUser"
      @manage-areas="manageUserAreas"
      @delete="deleteUser"
    />

    <!-- 导入规则说明对话框 -->
    <el-dialog
      v-model="showImportRules"
      title="CSV导入规则说明"
      width="600px"
      :close-on-click-modal="false"
    >
      <div class="import-rules">
        <h4>CSV文件格式要求：</h4>
        <ol>
          <li><strong>文件编码：</strong>UTF-8编码</li>
          <li><strong>分隔符：</strong>英文逗号(,)</li>
          <li><strong>表头：</strong>第一行必须包含字段名称</li>
        </ol>
        
        <h4>必填字段：</h4>
        <ul>
          <li><strong>用户名：</strong>唯一标识，不能重复</li>
          <li><strong>姓名：</strong>用户真实姓名</li>
          <li><strong>邮箱：</strong>有效邮箱地址，不能重复</li>
          <li><strong>工号：</strong>员工工号，不能重复</li>
          <li><strong>身份证号：</strong>18位身份证号码，不能重复</li>
          <li><strong>性别：</strong>male(男) 或 female(女)</li>
          <li><strong>入职时间：</strong>格式为YYYY-MM-DD</li>
        </ul>
        
        <h4>可选字段：</h4>
        <ul>
          <li><strong>电话：</strong>联系电话</li>
          <li><strong>职位：</strong>工作职位</li>
          <li><strong>部门：</strong>所属部门</li>
          <li><strong>区域：</strong>工作区域，多个区域用竖线(|)分隔，如：区域1|区域2|区域3</li>
          <li><strong>民族：</strong>民族信息</li>
          <li><strong>角色：</strong><span v-if="roleOptions.length > 0">{{ roleOptions.map(r => `${r.value}(${r.label})`).join('、') }}</span><span v-else>admin(管理员)、manager(经理)、user(用户)</span>，默认为user</li>
          <li><strong>状态：</strong>active(在职)、inactive(离职)，默认为active</li>
          <li><strong>是否驾驶员：</strong>true(是) 或 false(否)，默认为false</li>
          <li><strong>技能：</strong>多个技能用竖线(|)分隔</li>
          <li><strong>离职时间：</strong>格式为YYYY-MM-DD</li>
          <li><strong>电工证有效期：</strong>格式为YYYY-MM-DD</li>
          <li><strong>登高证有效期：</strong>格式为YYYY-MM-DD</li>
          <li><strong>批号：</strong>批次号码</li>
          <li><strong>备注：</strong>其他备注信息</li>
        </ul>
        
        <h4>注意事项：</h4>
        <ul>
          <li>导入的用户默认密码为：<code>defaultPassword123</code></li>
          <li>身份证号的第17位数字必须与性别匹配（奇数为男，偶数为女）</li>
          <li>如果字段值包含逗号，请用双引号包围该字段</li>
          <li>日期格式必须严格按照YYYY-MM-DD格式</li>
          <li>导入失败的记录会在控制台显示错误信息</li>
        </ul>
      </div>
      <template #footer>
        <el-button @click="showImportRules = false">关闭</el-button>
        <el-button type="primary" @click="downloadTemplate">下载模板</el-button>
      </template>
    </el-dialog>

    <!-- 用户表单对话框 -->
    <UserForm
      v-model:visible="showAddDialog"
      :editing-user="editingUser"
      :role-options="roleOptions"
      :area-options="areaOptions"
      :submitting="submitting"
      @submit="handleUserSubmit"
    />
    <!-- 导入规则对话框 -->
    <UserImportDialog v-model:visible="showImportRules" />

    <!-- 区域管理对话框 -->
    <UserAreaDialog
      v-model:visible="showAreaDialog"
      :user="selectedUser"
      :area-options="areaOptions"
      :submitting="submitting"
      @submit="handleAreaSubmit"
    />

  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { apiClient } from '@/api/axios'
import { areasApi } from '@/api/areas'
import { systemConfigApi } from '@/api/systemConfig'
import { loadRoleOptions as loadRoleOptionsUtil } from '@/utils/roleUtils'

// 导入拆分的组件
import PageHeader from '@/components/common/PageHeader.vue'
import SearchForm from '@/components/common/SearchForm.vue'
import UserTable from '@/components/business/UserTable.vue'
import UserForm from '@/components/business/UserForm.vue'
import UserImportDialog from '@/components/business/UserImportDialog.vue'
import UserAreaDialog from '@/components/business/UserAreaDialog.vue'

// 状态变量
const loading = ref(false)
const submitting = ref(false)
const showAddDialog = ref(false)
const showImportRules = ref(false)
const showAreaDialog = ref(false)
const editingUser = ref<any>(null)
const selectedUser = ref<any>(null)

// 数据
const users = ref<any[]>([])
const areaOptions = ref<any[]>([])
const roleOptions = ref<any[]>([])

// 事件处理函数
const editUser = (user: any) => {
  editingUser.value = user
  showAddDialog.value = true
}

const manageUserAreas = (user: any) => {
  selectedUser.value = user
  showAreaDialog.value = true
}

const deleteUser = async (user: any) => {
  try {
    await ElMessageBox.confirm('确定要删除这个用户吗？', '确认删除', {
      type: 'warning'
    })

    await apiClient.delete(`/users/${user.id}`)
    ElMessage.success('用户删除成功')
    await loadUsers()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('删除用户失败: ' + (error.message || error))
    }
  }
}

const handleUserSubmit = async (formData: any) => {
  submitting.value = true
  try {
    if (editingUser.value) {
      // 更新用户
      await apiClient.put(`/users/${editingUser.value.id}`, formData)
      ElMessage.success('用户更新成功')
    } else {
      // 创建用户
      await apiClient.post('/users', formData)
      ElMessage.success('用户创建成功')
    }

    showAddDialog.value = false
    editingUser.value = null
    await loadUsers()
  } catch (error: any) {
    ElMessage.error(editingUser.value ? '更新用户失败' : '创建用户失败')
  } finally {
    submitting.value = false
  }
}

const handleAreaSubmit = async (userId: number, areaIds: number[]) => {
  submitting.value = true
  try {
    await apiClient.post(`/users/${userId}/areas`, { area_ids: areaIds })
    ElMessage.success('区域权限设置成功')
    showAreaDialog.value = false
    selectedUser.value = null
    await loadUsers()
  } catch (error: any) {
    ElMessage.error('设置区域权限失败')
  } finally {
    submitting.value = false
  }
}

// 数据加载函数

const loadUsers = async () => {
  loading.value = true
  try {
    const response = await apiClient.get('/users', { params: { page: 1, per_page: 100 } })
    users.value = response.data.users || []
  } catch (error: any) {
    ElMessage.error('加载用户数据失败: ' + (error.message || error))
  } finally {
    loading.value = false
  }
}

const loadAreaOptions = async () => {
  try {
    const response = await areasApi.getAreaOptions()
    console.log('区域选项API响应:', response.data)
    
    // 检查响应数据是否为数组
    if (!Array.isArray(response.data)) {
      console.error('区域选项API返回的数据不是数组:', response.data)
      ElMessage.error('区域数据格式错误')
      areaOptions.value = []
      return
    }
    
    // 后端返回的是直接的数组格式 [{value: id, label: name}]
    // 需要转换为前端期望的格式 [{id: id, name: name}]
    areaOptions.value = response.data.map((area: any) => ({
      id: area.value,
      name: area.label
    }))
  } catch (error: any) {
    console.error('加载区域选项失败:', error)
    console.error('错误详情:', error.response?.data)
    ElMessage.error(`加载区域数据失败: ${error.response?.data?.error || error.message}`)
    areaOptions.value = []
  }
}

const loadRoleOptions = async () => {
  try {
    roleOptions.value = await loadRoleOptionsUtil()
  } catch (error) {
    console.error('加载角色选项失败:', error)
    roleOptions.value = []
  }
}

// 工具函数
const getRoleLabel = (roleValue: string) => {
  const role = roleOptions.value.find(r => r.value === roleValue)
  return role ? role.label : roleValue
}

onMounted(() => {
  loadUsers()
  loadAreaOptions()
  loadRoleOptions()
})

// 导出用户数据为CSV文件
const exportUsers = () => {
  if (users.value.length === 0) {
    ElMessage.warning("没有可导出的用户数据")
    return
  }
  const headers = [
    "用户名", "姓名", "邮箱", "工号", "电话", "职位", "部门", "区域", "身份证号", "民族", "性别", 
    "入职时间", "角色", "状态", "是否驾驶员", "技能", "离职时间", "电工证有效期", "登高证有效期", "批号", "备注"
  ]
  const csvRows = []
  csvRows.push(headers.join(","))
  users.value.forEach(user => {
    const row = [
      user.username || "",
      user.name || "",
      user.email || "",
      user.employee_id || "",
      user.phone || "",
      user.position || "",
      user.department || "",
      (user.areas && user.areas.length > 0) ? user.areas.join('|') : (user.area_name || ""),
      user.id_card || "",
      user.ethnicity || "",
      user.gender || "",
      user.hire_date || "",
      user.role || "user",
      user.status || "active",
      user.is_driver ? "true" : "false",
      (user.skills || []).join("|"),
      user.resignation_date || "",
      user.electrician_cert_expiry || "",
      user.height_cert_expiry || "",
      user.batch_number || "",
      user.remarks || ""
    ]
    // 转义逗号和双引号
    const escapedRow = row.map(field => {
      if (typeof field === "string" && (field.includes(",") || field.includes('"'))) {
        return `"${field.replace(/"/g, '""')}"`
      }
      return field
    })
    csvRows.push(escapedRow.join(","))
  })
  const csvString = "\uFEFF" + csvRows.join("\n") // 添加BOM以支持中文
  const blob = new Blob([csvString], { type: "text/csv;charset=utf-8;" })
  const url = URL.createObjectURL(blob)
  const a = document.createElement("a")
  a.href = url
  a.download = "users_export.csv"
  a.click()
  URL.revokeObjectURL(url)
  ElMessage.success("导出成功")
}

// 导入前阻止自动上传
const beforeUpload = () => {
  return false
}

// 处理导入的CSV文件
const handleImport = (file: any) => {
  const reader = new FileReader()
  reader.onload = async (e: any) => {
    try {
      const csvText = e.target.result
      const lines = csvText.split('\n').filter((line: string) => line.trim())
      if (lines.length < 2) {
        ElMessage.error("CSV文件格式错误，至少需要表头和一行数据")
        return
      }
      
      const headers = lines[0].split(',').map((h: string) => h.trim().replace(/^"|"$/g, ''))
      const importedUsers = []
      
      for (let i = 1; i < lines.length; i++) {
        const values = parseCSVLine(lines[i])
        if (values.length !== headers.length) {
          console.warn(`第${i + 1}行数据列数不匹配，跳过`)
          continue
        }
        
        const user: any = {}
        headers.forEach((header, index) => {
          const value = values[index]?.trim()
          switch (header) {
            case '用户名':
              user.username = value
              break
            case '姓名':
              user.name = value
              break
            case '邮箱':
              user.email = value
              break
            case '工号':
              user.employee_id = value
              break
            case '电话':
              user.phone = value
              break
            case '职位':
              user.position = value
              break
            case '部门':
              user.department = value
              break
            case '区域':
              // 支持多区域导入，用竖线(|)分隔
              if (value && value.includes('|')) {
                user.area = value.split('|').map(a => a.trim()).filter(a => a)
              } else {
                user.area = value ? [value] : []
              }
              break
            case '身份证号':
              user.id_card = value
              break
            case '民族':
              user.ethnicity = value
              break
            case '性别':
              user.gender = value
              break
            case '入职时间':
              user.hire_date = value
              break
            case '角色':
              user.role = value || 'user'
              break
            case '状态':
              user.status = value || 'active'
              break
            case '是否驾驶员':
              user.is_driver = value === 'true'
              break
            case '技能':
              user.skills = value ? value.split('|') : []
              break
            case '离职时间':
              user.resignation_date = value
              break
            case '电工证有效期':
              user.electrician_cert_expiry = value
              break
            case '登高证有效期':
              user.height_cert_expiry = value
              break
            case '批号':
              user.batch_number = value
              break
            case '备注':
              user.remarks = value
              break
          }
        })
        
        if (user.username && user.name && user.email && user.employee_id && user.id_card && user.gender && user.hire_date) {
          importedUsers.push(user)
        } else {
          console.warn(`第${i + 1}行缺少必填字段，跳过:`, user)
        }
      }
      
      let successCount = 0
      let failCount = 0
      
      for (const user of importedUsers) {
        try {
          await apiClient.post('/users', {
            ...user,
            password: 'defaultPassword123' // 导入用户默认密码
          })
          successCount++
        } catch (err: any) {
          failCount++
          console.error('导入用户失败:', user.username, err.response?.data?.error || err.message)
        }
      }
      
      ElMessage.success(`导入完成！成功：${successCount}条，失败：${failCount}条`)
      await loadUsers()
    } catch (error) {
      ElMessage.error("CSV文件解析失败，请检查文件格式")
      console.error('CSV解析错误:', error)
    }
  }
  reader.readAsText(file.raw, 'UTF-8')
}

// 解析CSV行，处理逗号和引号
const parseCSVLine = (line: string): string[] => {
  const result = []
  let current = ''
  let inQuotes = false
  
  for (let i = 0; i < line.length; i++) {
    const char = line[i]
    
    if (char === '"') {
      if (inQuotes && line[i + 1] === '"') {
        current += '"'
        i++ // 跳过下一个引号
      } else {
        inQuotes = !inQuotes
      }
    } else if (char === ',' && !inQuotes) {
      result.push(current)
      current = ''
    } else {
      current += char
    }
  }
  
  result.push(current)
  return result
}

// 下载CSV模板
const downloadTemplate = () => {
  const headers = [
    "用户名", "姓名", "邮箱", "工号", "电话", "职位", "部门", "区域", "身份证号", "民族", "性别", 
    "入职时间", "角色", "状态", "是否驾驶员", "技能", "离职时间", "电工证有效期", "登高证有效期", "批号", "备注"
  ]
  const sampleData = [
    "zhangsan", "张三", "<EMAIL>", "EMP001", "13800138000", "工程师", "技术部", "A区", 
    "110101199001011234", "汉族", "male", "2024-01-01", "user", "active", "false", "编程|测试", 
    "", "", "", "BATCH001", "示例用户"
  ]
  
  const csvContent = "\uFEFF" + headers.join(",") + "\n" + sampleData.join(",")
  const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" })
  const url = URL.createObjectURL(blob)
  const a = document.createElement("a")
  a.href = url
  a.download = "user_import_template.csv"
  a.click()
  URL.revokeObjectURL(url)
  ElMessage.success("模板下载成功")
}

</script>

<style scoped>
.user-management {
  width: 100%;
}

.upload-btn {
  display: inline-block;
}
</style>