graph TD
    A[App.vue 根组件] --> B[MainLayout.vue 主布局]
    A --> C[LoginView.vue 登录页]
    A --> D[MobileLoginView.vue 移动端登录]
    
    B --> E[DashboardView.vue 仪表板]
    B --> F[工单管理模块]
    B --> G[库存管理模块]
    B --> H[系统管理模块]
    B --> I[测试页面]
    
    F --> F1[WorkOrdersView.vue 工单列表]
    F --> F2[WorkOrderCreateView.vue 创建工单]
    F --> F3[WorkOrderDetailView.vue 工单详情]
    F --> F4[WorkOrderEditView.vue 编辑工单]
    F --> F5[WorkOrderPendingView.vue 待办工单]
    F --> F6[WorkOrderCompletedView.vue 已完成工单]
    F --> F7[WorkOrderReportsView.vue 工单报表]
    F --> F8[WorkOrderConfigView.vue 工单配置]
    
    G --> G1[InventoryMaterialsView.vue 材料库存]
    G --> G2[InventoryUsageView.vue 使用清单]
    
    H --> H1[UserManagementView.vue 用户管理]
    H --> H2[AreaManagementView.vue 区域管理]
    H --> H3[PermissionManagementView.vue 权限管理]
    H --> H4[SystemConfigView.vue 系统配置]
    H --> H5[MaterialCategoryView.vue 物料分类]
    H --> H6[FormTemplateView.vue 表单模板]
    
    I --> I1[StyleTestView.vue 样式测试]
    I --> I2[DeviceTestView.vue 设备测试]
    
    A --> J[移动端模块]
    J --> J1[MobileMaintenanceView.vue 移动端主页]
    J --> J2[MobileWorkOrderDetailView.vue 移动端工单详情]
    J --> J3[MobileWorkOrderEditView.vue 移动端工单编辑]
    
    K[组件库] --> K1[mobile/ 移动端组件]
    K --> K2[icons/ 图标组件]
    K --> K3[MainLayout.vue 主布局组件]
    
    K1 --> K11[MobileHeader.vue 移动端头部]
    K1 --> K12[TabBar.vue 底部导航]
    K1 --> K13[WorkOrderList.vue 工单列表]
    K1 --> K14[StatsCards.vue 统计卡片]
    K1 --> K15[CompleteDialog.vue 完成对话框]
    K1 --> K16[FeedbackDialog.vue 反馈对话框]
    
    L[状态管理] --> L1[auth.ts 认证状态]
    L --> L2[workOrders.ts 工单状态]
    
    M[API层] --> M1[axios.ts HTTP客户端]
    M --> M2[auth.ts 认证API]
    M --> M3[workOrders.ts 工单API]
    M --> M4[materials.ts 材料API]
    M --> M5[users.ts 用户API]
    M --> M6[其他API模块...]
    
    N[工具函数] --> N1[device.ts 设备检测]
    N --> N2[roleUtils.ts 角色工具]
    N --> N3[networkUtils.ts 网络工具]
    
    O[Composables] --> O1[useWorkOrders.ts 工单逻辑]
    O --> O2[useMaterials.ts 材料逻辑]
    O --> O3[useWorkOrderActions.ts 工单操作]