#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
执行所有数据库迁移脚本
按照优化建议的实施顺序执行所有迁移
"""

import os
import sys
import subprocess
from datetime import datetime

def get_migration_scripts():
    """获取所有迁移脚本列表（按执行顺序）"""
    return [
        'create_system_dictionaries.py',
        'create_material_categories.py', 
        'create_form_field_templates.py',
        'create_user_permissions.py',
        'create_system_configs.py'
    ]

def run_migration(script_name):
    """运行单个迁移脚本"""
    print(f"\n{'='*60}")
    print(f"正在执行迁移: {script_name}")
    print(f"{'='*60}")
    
    try:
        # 获取当前脚本所在目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        script_path = os.path.join(current_dir, script_name)
        
        if not os.path.exists(script_path):
            print(f"❌ 迁移脚本不存在: {script_path}")
            return False
        
        # 执行迁移脚本
        result = subprocess.run(
            [sys.executable, script_path],
            capture_output=True,
            text=True,
            cwd=os.path.dirname(current_dir)  # 在backend目录下执行
        )
        
        if result.returncode == 0:
            print(f"✅ {script_name} 执行成功")
            if result.stdout:
                print(result.stdout)
            return True
        else:
            print(f"❌ {script_name} 执行失败")
            if result.stderr:
                print(f"错误信息: {result.stderr}")
            if result.stdout:
                print(f"输出信息: {result.stdout}")
            return False
            
    except Exception as e:
        print(f"❌ 执行 {script_name} 时发生异常: {str(e)}")
        return False

def check_database_exists():
    """检查数据库文件是否存在"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    backend_dir = os.path.dirname(current_dir)
    db_path = os.path.join(backend_dir, 'instance', 'dispatch_system.db')
    
    if os.path.exists(db_path):
        print(f"✅ 数据库文件存在: {db_path}")
        return True
    else:
        print(f"❌ 数据库文件不存在: {db_path}")
        print("请先启动应用程序以创建数据库文件")
        return False

def create_migration_log(success_count, total_count, failed_scripts):
    """创建迁移日志"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    log_file = os.path.join(current_dir, 'migration_log.txt')
    
    with open(log_file, 'w', encoding='utf-8') as f:
        f.write(f"数据库迁移执行日志\n")
        f.write(f"执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"总计迁移: {total_count} 个\n")
        f.write(f"成功执行: {success_count} 个\n")
        f.write(f"执行失败: {len(failed_scripts)} 个\n\n")
        
        if failed_scripts:
            f.write("失败的迁移脚本:\n")
            for script in failed_scripts:
                f.write(f"- {script}\n")
        else:
            f.write("所有迁移脚本执行成功！\n")
    
    print(f"\n📝 迁移日志已保存到: {log_file}")

def main():
    """主函数"""
    print("🚀 开始执行数据库优化迁移")
    print(f"执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查数据库是否存在
    if not check_database_exists():
        return
    
    # 获取迁移脚本列表
    migration_scripts = get_migration_scripts()
    total_count = len(migration_scripts)
    success_count = 0
    failed_scripts = []
    
    print(f"\n📋 计划执行 {total_count} 个迁移脚本:")
    for i, script in enumerate(migration_scripts, 1):
        print(f"  {i}. {script}")
    
    print("\n开始执行迁移...")
    
    # 逐个执行迁移脚本
    for script in migration_scripts:
        if run_migration(script):
            success_count += 1
        else:
            failed_scripts.append(script)
            # 询问是否继续执行后续迁移
            response = input(f"\n⚠️  {script} 执行失败，是否继续执行后续迁移？(y/n): ")
            if response.lower() != 'y':
                print("用户选择停止执行后续迁移")
                break
    
    # 输出执行结果
    print(f"\n{'='*60}")
    print("🎯 迁移执行完成")
    print(f"{'='*60}")
    print(f"总计迁移: {total_count} 个")
    print(f"成功执行: {success_count} 个")
    print(f"执行失败: {len(failed_scripts)} 个")
    
    if failed_scripts:
        print(f"\n❌ 失败的迁移脚本:")
        for script in failed_scripts:
            print(f"  - {script}")
        print("\n建议检查失败的脚本并手动执行")
    else:
        print("\n🎉 所有迁移脚本执行成功！")
        print("\n✨ 数据库优化完成，已实现:")
        print("  1. ✅ 系统字典表 - 统一管理选项数据")
        print("  2. ✅ 材料分类表 - 支持层级分类管理")
        print("  3. ✅ 表单字段模板表 - 动态表单配置")
        print("  4. ✅ 用户权限配置表 - 细粒度权限控制")
        print("  5. ✅ 系统配置表 - 参数化配置管理")
        print("\n🔄 下一步建议:")
        print("  1. 创建相应的API接口")
        print("  2. 更新前端组件使用新的数据结构")
        print("  3. 逐步替换硬编码数据")
        print("  4. 迁移现有数据到新的表结构")
    
    # 创建迁移日志
    create_migration_log(success_count, total_count, failed_scripts)
    
    print(f"\n📊 详细执行日志请查看: migration_log.txt")

if __name__ == '__main__':
    main()