#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建系统配置表迁移脚本
用于管理系统参数配置，替代前端硬编码的配置项
"""

import sqlite3
import os
import json
from datetime import datetime

def get_db_path():
    """获取数据库路径"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    backend_dir = os.path.dirname(current_dir)
    db_path = os.path.join(backend_dir, 'instance', 'dispatch_system.db')
    return db_path

def create_system_configs_table():
    """创建系统配置表"""
    db_path = get_db_path()
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查表是否已存在
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='system_configs'
        """)
        
        if cursor.fetchone():
            print("system_configs 表已存在，跳过创建")
            return True
        
        # 创建系统配置表
        cursor.execute("""
            CREATE TABLE system_configs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                config_key VARCHAR(100) NOT NULL UNIQUE, -- 配置键
                config_value TEXT,                       -- 配置值
                config_type VARCHAR(50) DEFAULT 'string', -- 配置类型
                category VARCHAR(50) NOT NULL,           -- 配置分类
                name VARCHAR(100) NOT NULL,              -- 配置名称
                description TEXT,                        -- 配置描述
                default_value TEXT,                      -- 默认值
                validation_rule TEXT,                    -- 验证规则(JSON)
                is_public BOOLEAN DEFAULT FALSE,        -- 是否公开(前端可访问)
                is_editable BOOLEAN DEFAULT TRUE,       -- 是否可编辑
                sort_order INTEGER DEFAULT 0,           -- 排序
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        print("✅ system_configs 表创建成功")
        
        # 插入初始化数据
        insert_initial_configs(cursor)
        
        conn.commit()
        conn.close()
        
        print("✅ 系统配置表创建和初始化完成")
        return True
        
    except Exception as e:
        print(f"❌ 创建系统配置表失败: {str(e)}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

def insert_initial_configs(cursor):
    """插入初始化配置数据"""
    
    # 系统配置数据
    configs_data = [
        # 系统基础配置
        ('system.name', '派单管理系统', 'string', 'system', '系统名称', '系统的显示名称', '派单管理系统', None, True, True, 1),
        ('system.version', '1.0.0', 'string', 'system', '系统版本', '当前系统版本号', '1.0.0', None, True, False, 2),
        ('system.logo_url', '/static/images/logo.png', 'string', 'system', '系统Logo', '系统Logo图片路径', '/static/images/logo.png', None, True, True, 3),
        ('system.favicon_url', '/static/images/favicon.ico', 'string', 'system', '网站图标', '浏览器标签页图标', '/static/images/favicon.ico', None, True, True, 4),
        ('system.copyright', '© 2024 派单管理系统', 'string', 'system', '版权信息', '系统版权声明', '© 2024 派单管理系统', None, True, True, 5),
        
        # 工单配置
        ('work_order.auto_assign', 'false', 'boolean', 'work_order', '自动分配工单', '是否启用工单自动分配功能', 'false', None, False, True, 10),
        ('work_order.default_priority', 'medium', 'string', 'work_order', '默认优先级', '新建工单的默认优先级', 'medium', json.dumps({'options': ['low', 'medium', 'high', 'urgent']}), True, True, 11),
        ('work_order.max_attachments', '5', 'number', 'work_order', '最大附件数', '工单可上传的最大附件数量', '5', json.dumps({'min': 1, 'max': 20}), True, True, 12),
        ('work_order.attachment_max_size', '10', 'number', 'work_order', '附件最大大小(MB)', '单个附件的最大大小限制', '10', json.dumps({'min': 1, 'max': 100}), True, True, 13),
        ('work_order.auto_close_days', '30', 'number', 'work_order', '自动关闭天数', '已完成工单多少天后自动关闭', '30', json.dumps({'min': 1, 'max': 365}), False, True, 14),
        ('work_order.require_completion_photo', 'false', 'boolean', 'work_order', '必须上传完成照片', '完成工单时是否必须上传照片', 'false', None, True, True, 15),
        
        # 材料管理配置
        ('material.low_stock_alert', 'true', 'boolean', 'material', '低库存预警', '是否启用低库存预警功能', 'true', None, False, True, 20),
        ('material.alert_threshold_ratio', '0.2', 'number', 'material', '预警阈值比例', '库存低于最低库存的比例时预警', '0.2', json.dumps({'min': 0.1, 'max': 1.0, 'step': 0.1}), False, True, 21),
        ('material.auto_deduct_stock', 'true', 'boolean', 'material', '自动扣减库存', '工单使用材料时是否自动扣减库存', 'true', None, False, True, 22),
        ('material.require_approval_amount', '1000', 'number', 'material', '需要审批的金额', '材料使用超过此金额需要审批', '1000', json.dumps({'min': 0}), False, True, 23),
        
        # 用户管理配置
        ('user.default_role', 'user', 'string', 'user', '默认用户角色', '新用户的默认角色', 'user', json.dumps({'options': ['admin', 'manager', 'user']}), False, True, 30),
        ('user.password_min_length', '6', 'number', 'user', '密码最小长度', '用户密码的最小长度要求', '6', json.dumps({'min': 4, 'max': 20}), True, True, 31),
        ('user.password_require_special', 'false', 'boolean', 'user', '密码需要特殊字符', '密码是否必须包含特殊字符', 'false', None, True, True, 32),
        ('user.session_timeout', '24', 'number', 'user', '会话超时时间(小时)', '用户会话的超时时间', '24', json.dumps({'min': 1, 'max': 168}), False, True, 33),
        ('user.max_login_attempts', '5', 'number', 'user', '最大登录尝试次数', '账户锁定前的最大登录失败次数', '5', json.dumps({'min': 3, 'max': 10}), False, True, 34),
        
        # 通知配置
        ('notification.email_enabled', 'false', 'boolean', 'notification', '启用邮件通知', '是否启用邮件通知功能', 'false', None, False, True, 40),
        ('notification.sms_enabled', 'false', 'boolean', 'notification', '启用短信通知', '是否启用短信通知功能', 'false', None, False, True, 41),
        ('notification.work_order_created', 'true', 'boolean', 'notification', '工单创建通知', '创建工单时是否发送通知', 'true', None, True, True, 42),
        ('notification.work_order_assigned', 'true', 'boolean', 'notification', '工单分配通知', '分配工单时是否发送通知', 'true', None, True, True, 43),
        ('notification.work_order_completed', 'true', 'boolean', 'notification', '工单完成通知', '完成工单时是否发送通知', 'true', None, True, True, 44),
        
        # 界面配置
        ('ui.theme', 'light', 'string', 'ui', '默认主题', '系统的默认主题', 'light', json.dumps({'options': ['light', 'dark', 'auto']}), True, True, 50),
        ('ui.language', 'zh-CN', 'string', 'ui', '默认语言', '系统的默认语言', 'zh-CN', json.dumps({'options': ['zh-CN', 'en-US']}), True, True, 51),
        ('ui.page_size', '20', 'number', 'ui', '默认分页大小', '列表页面的默认分页大小', '20', json.dumps({'min': 10, 'max': 100, 'step': 10}), True, True, 52),
        ('ui.show_welcome_guide', 'true', 'boolean', 'ui', '显示欢迎引导', '新用户是否显示欢迎引导', 'true', None, True, True, 53),
        ('ui.auto_refresh_interval', '30', 'number', 'ui', '自动刷新间隔(秒)', '页面自动刷新的间隔时间，0表示不自动刷新', '30', json.dumps({'min': 0, 'max': 300}), True, True, 54),
        
        # 报告配置
        ('report.default_date_range', '30', 'number', 'report', '默认日期范围(天)', '报告的默认日期范围', '30', json.dumps({'min': 1, 'max': 365}), True, True, 60),
        ('report.max_export_records', '10000', 'number', 'report', '最大导出记录数', '单次导出的最大记录数', '10000', json.dumps({'min': 100, 'max': 100000}), False, True, 61),
        ('report.cache_duration', '300', 'number', 'report', '报告缓存时间(秒)', '报告数据的缓存时间', '300', json.dumps({'min': 60, 'max': 3600}), False, True, 62),
        
        # 安全配置
        ('security.enable_captcha', 'false', 'boolean', 'security', '启用验证码', '登录时是否启用验证码', 'false', None, False, True, 70),
        ('security.enable_2fa', 'false', 'boolean', 'security', '启用双因子认证', '是否启用双因子认证', 'false', None, False, True, 71),
        ('security.api_rate_limit', '100', 'number', 'security', 'API速率限制', '每分钟API调用次数限制', '100', json.dumps({'min': 10, 'max': 1000}), False, True, 72),
        ('security.log_retention_days', '90', 'number', 'security', '日志保留天数', '系统日志的保留天数', '90', json.dumps({'min': 7, 'max': 365}), False, True, 73),
        
        # 备份配置
        ('backup.auto_backup', 'false', 'boolean', 'backup', '自动备份', '是否启用自动备份功能', 'false', None, False, True, 80),
        ('backup.backup_interval', '24', 'number', 'backup', '备份间隔(小时)', '自动备份的间隔时间', '24', json.dumps({'min': 1, 'max': 168}), False, True, 81),
        ('backup.max_backup_files', '7', 'number', 'backup', '最大备份文件数', '保留的最大备份文件数量', '7', json.dumps({'min': 1, 'max': 30}), False, True, 82)
    ]
    
    # 插入配置数据
    cursor.executemany("""
        INSERT INTO system_configs 
        (config_key, config_value, config_type, category, name, description, default_value, validation_rule, is_public, is_editable, sort_order)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    """, configs_data)
    
    print(f"✅ 插入了 {len(configs_data)} 个系统配置项")
    
    # 统计各分类的配置数量
    cursor.execute("""
        SELECT category, COUNT(*) as count 
        FROM system_configs 
        GROUP BY category 
        ORDER BY category
    """)
    
    categories = cursor.fetchall()
    for category, count in categories:
        print(f"   - {category}: {count} 个配置项")

def create_config_history_table(cursor):
    """创建配置变更历史表"""
    cursor.execute("""
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name='config_history'
    """)
    
    if cursor.fetchone():
        print("config_history 表已存在，跳过创建")
        return
    
    cursor.execute("""
        CREATE TABLE config_history (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            config_key VARCHAR(100) NOT NULL,
            old_value TEXT,
            new_value TEXT,
            changed_by INTEGER,
            changed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            change_reason TEXT,
            FOREIGN KEY (changed_by) REFERENCES users(id)
        )
    """)
    
    print("✅ config_history 表创建成功")

def main():
    """主函数"""
    print("开始创建系统配置表...")
    
    if create_system_configs_table():
        print("\n🎉 系统配置表创建成功！")
        print("\n已完成：")
        print("1. ✅ 创建system_configs表")
        print("2. ✅ 插入系统配置数据")
        print("3. ✅ 支持配置分类和验证")
        print("\n配置分类包括：")
        print("- system: 系统基础配置")
        print("- work_order: 工单管理配置")
        print("- material: 材料管理配置")
        print("- user: 用户管理配置")
        print("- notification: 通知配置")
        print("- ui: 界面配置")
        print("- report: 报告配置")
        print("- security: 安全配置")
        print("- backup: 备份配置")
        print("\n下一步可以：")
        print("1. 创建配置管理API接口")
        print("2. 创建系统配置管理页面")
        print("3. 替换前端硬编码的配置项")
    else:
        print("\n❌ 系统配置表创建失败")

if __name__ == '__main__':
    main()