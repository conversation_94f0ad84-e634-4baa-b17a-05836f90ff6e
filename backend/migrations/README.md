# 数据库优化迁移说明

## 概述

本次数据库优化基于前端硬编码数据分析报告，通过创建新的数据表来消除前端硬编码问题，提高系统的可维护性和可扩展性。

## 优化内容

### 1. 系统字典表 (system_dictionaries)

**目的**: 统一管理所有选项数据，替代前端硬编码的下拉选项

**表结构**:
- `dict_type`: 字典类型（如：work_order_type, priority等）
- `dict_key`: 字典键值
- `dict_label`: 显示标签
- `dict_value`: 字典值（可选）
- `sort_order`: 排序
- `is_active`: 是否启用
- `is_system`: 是否系统内置

**包含数据**:
- 工单类型、优先级、状态
- 材料分类、库存状态、使用类型
- 用户角色、状态、性别
- 故障原因、满意度评级等

### 2. 材料分类表 (material_categories)

**目的**: 支持材料的层级分类管理，替代硬编码的材料类型

**表结构**:
- `name`: 分类名称
- `code`: 分类编码
- `parent_id`: 父分类ID
- `level`: 分类层级
- `path`: 分类路径
- `sort_order`: 排序

**特性**:
- 支持多级分类（当前设计为2级）
- 为materials表添加了category_id字段
- 包含电子元件、机械配件、办公用品等主要分类

### 3. 表单字段模板表 (form_field_templates)

**目的**: 动态配置表单字段，替代前端硬编码的表单结构

**表结构**:
- `form_type`: 表单类型
- `field_name`: 字段名称
- `field_label`: 字段标签
- `field_type`: 字段类型（input, select, textarea等）
- `field_options`: 字段选项（JSON格式）
- `validation_rules`: 验证规则（JSON格式）
- `default_value`: 默认值
- `placeholder`: 占位符
- `help_text`: 帮助文本

**包含模板**:
- 工单创建表单（8个字段）
- 工单处理表单（5个字段）
- 材料管理表单（8个字段）
- 用户管理表单（7个字段）

### 4. 用户权限配置表 (permissions, role_permissions, user_permissions)

**目的**: 实现细粒度的权限控制，替代前端硬编码的权限判断

**表结构**:
- `permissions`: 权限定义表
- `role_permissions`: 角色权限关联表
- `user_permissions`: 用户特殊权限表

**权限模块**:
- 工单管理（8个权限）
- 材料管理（6个权限）
- 用户管理（7个权限）
- 区域管理（4个权限）
- 组织管理（4个权限）
- 车辆管理（4个权限）
- 报告统计（3个权限）
- 系统管理（5个权限）

**角色权限分配**:
- 管理员：拥有所有43个权限
- 经理：拥有25个权限
- 用户：拥有7个基础权限

### 5. 系统配置表 (system_configs)

**目的**: 参数化配置管理，替代前端硬编码的配置项

**表结构**:
- `config_key`: 配置键
- `config_value`: 配置值
- `config_type`: 配置类型（string, number, boolean）
- `category`: 配置分类
- `name`: 配置名称
- `description`: 配置描述
- `validation_rule`: 验证规则（JSON格式）
- `is_public`: 是否公开（前端可访问）
- `is_editable`: 是否可编辑

**配置分类**:
- system: 系统基础配置（5项）
- work_order: 工单管理配置（6项）
- material: 材料管理配置（4项）
- user: 用户管理配置（5项）
- notification: 通知配置（5项）
- ui: 界面配置（5项）
- report: 报告配置（3项）
- security: 安全配置（4项）
- backup: 备份配置（3项）

## 迁移脚本说明

### 单独执行脚本

1. **create_system_dictionaries.py** - 创建系统字典表
2. **create_material_categories.py** - 创建材料分类表
3. **create_form_field_templates.py** - 创建表单字段模板表
4. **create_user_permissions.py** - 创建用户权限配置表
5. **create_system_configs.py** - 创建系统配置表

### 批量执行脚本

- **run_all_migrations.py** - 按顺序执行所有迁移脚本
- **verify_database_structure.py** - 验证数据库表结构

## 使用方法

### 1. 执行迁移

```bash
# 方法1：执行所有迁移
cd /home/<USER>/dispatch-system
python3 backend/migrations/run_all_migrations.py

# 方法2：单独执行迁移
python3 backend/migrations/create_system_dictionaries.py
python3 backend/migrations/create_material_categories.py
# ... 其他脚本
```

### 2. 验证结果

```bash
# 验证数据库表结构
python3 backend/migrations/verify_database_structure.py
```

### 3. 查看日志

- `migration_log.txt` - 迁移执行日志
- `verification_report.txt` - 验证报告

## 执行结果

✅ **已成功创建的表**:
- system_dictionaries (46条记录)
- material_categories (27条记录)
- form_field_templates (28条记录)
- permissions (43条记录)
- role_permissions (75条记录)
- user_permissions (空表，用于特殊权限分配)
- system_configs (40条记录)

✅ **已优化的现有表**:
- materials表添加了category_id字段

## 下一步建议

### 阶段二：API接口开发

1. **字典管理API**
   - GET /api/dictionaries/{type} - 获取字典数据
   - POST/PUT/DELETE /api/dictionaries - 管理字典项

2. **分类管理API**
   - GET /api/material-categories - 获取材料分类树
   - POST/PUT/DELETE /api/material-categories - 管理分类

3. **表单模板API**
   - GET /api/form-templates/{type} - 获取表单模板
   - POST/PUT /api/form-templates - 管理模板

4. **权限管理API**
   - GET /api/permissions - 获取权限列表
   - GET /api/users/{id}/permissions - 获取用户权限
   - POST /api/users/{id}/permissions - 分配权限

5. **配置管理API**
   - GET /api/configs - 获取配置列表
   - PUT /api/configs/{key} - 更新配置

### 阶段三：前端组件更新

1. **创建动态组件**
   - DynamicSelect - 基于字典的下拉选择
   - DynamicForm - 基于模板的动态表单
   - PermissionGuard - 权限控制组件
   - ConfigProvider - 配置提供者

2. **替换硬编码**
   - 逐步替换前端硬编码的选项数据
   - 更新表单组件使用动态模板
   - 实现基于权限的界面控制
   - 使用配置化的系统参数

### 阶段四：数据迁移

1. **现有数据迁移**
   - 将现有材料数据关联到分类
   - 迁移用户权限到新的权限系统
   - 配置系统参数的初始值

2. **测试验证**
   - 功能测试
   - 性能测试
   - 兼容性测试

## 预期效果

1. **可维护性提升**
   - 消除前端硬编码，便于维护
   - 统一数据管理，减少重复
   - 支持动态配置，无需重新部署

2. **可扩展性增强**
   - 支持新的字典类型和选项
   - 支持复杂的表单配置
   - 支持细粒度的权限控制
   - 支持系统参数的灵活配置

3. **用户体验改善**
   - 统一的界面风格
   - 灵活的权限控制
   - 个性化的配置选项
   - 更好的数据组织结构

## 注意事项

1. **备份数据库**
   - 执行迁移前请备份现有数据库
   - 建议在测试环境先验证

2. **权限兼容**
   - 新的权限系统需要与现有认证系统集成
   - 需要更新前端权限检查逻辑

3. **性能考虑**
   - 字典数据建议添加缓存
   - 权限检查需要优化查询性能
   - 配置数据可考虑内存缓存

4. **向后兼容**
   - 保持现有API的兼容性
   - 逐步迁移，避免影响现有功能

---

**创建时间**: 2024年12月
**版本**: 1.0.0
**状态**: 已完成数据库表创建和验证