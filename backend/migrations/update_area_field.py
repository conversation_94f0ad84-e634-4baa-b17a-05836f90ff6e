import os
import sys
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))  

# 设置环境变量
os.environ['DATABASE_URL'] = 'sqlite:////home/<USER>/dispatch-system/backend/instance/dispatch_system.db'

from wsgi import create_app
from app.models import db
from app.models.user import User

app = create_app()

with app.app_context():
    print("开始更新用户区域字段格式...")
    
    # 获取所有用户
    users = User.query.all()
    updated_count = 0
    
    for user in users:
        # 检查当前区域字段是否已经是JSON格式
        try:
            json.loads(user.area)
            # 如果能成功解析为JSON，说明已经是JSON格式，跳过
            continue
        except (json.JSONDecodeError, TypeError):
            # 不是JSON格式，需要转换
            pass
        
        # 将字符串格式转换为JSON数组格式
        if user.area and isinstance(user.area, str):
            user.area = json.dumps([user.area], ensure_ascii=False)
            updated_count += 1
        else:
            # 如果为空或其他类型，设置为空数组
            user.area = json.dumps([], ensure_ascii=False)
            updated_count += 1
    
    # 提交更改
    db.session.commit()
    
    print(f"更新完成，共更新了 {updated_count} 个用户的区域字段格式。")