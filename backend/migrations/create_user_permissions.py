#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建用户权限配置表迁移脚本
用于细粒度的权限控制，替代前端硬编码的权限判断
"""

import sqlite3
import os
import json
from datetime import datetime

def get_db_path():
    """获取数据库路径"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    backend_dir = os.path.dirname(current_dir)
    db_path = os.path.join(backend_dir, 'instance', 'dispatch_system.db')
    return db_path

def create_permissions_tables():
    """创建权限相关表"""
    db_path = get_db_path()
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 创建权限表
        create_permissions_table(cursor)
        
        # 创建角色权限关联表
        create_role_permissions_table(cursor)
        
        # 创建用户权限关联表（用于特殊权限分配）
        create_user_permissions_table(cursor)
        
        # 插入初始化数据
        insert_initial_permissions(cursor)
        
        conn.commit()
        conn.close()
        
        print("✅ 用户权限配置表创建和初始化完成")
        return True
        
    except Exception as e:
        print(f"❌ 创建用户权限配置表失败: {str(e)}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

def create_permissions_table(cursor):
    """创建权限表"""
    cursor.execute("""
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name='permissions'
    """)
    
    if cursor.fetchone():
        print("permissions 表已存在，跳过创建")
        return
    
    cursor.execute("""
        CREATE TABLE permissions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(100) NOT NULL UNIQUE,       -- 权限名称
            code VARCHAR(100) NOT NULL UNIQUE,       -- 权限代码
            module VARCHAR(50) NOT NULL,             -- 所属模块
            description TEXT,                        -- 权限描述
            is_active BOOLEAN DEFAULT TRUE,         -- 是否启用
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    """)
    
    print("✅ permissions 表创建成功")

def create_role_permissions_table(cursor):
    """创建角色权限关联表"""
    cursor.execute("""
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name='role_permissions'
    """)
    
    if cursor.fetchone():
        print("role_permissions 表已存在，跳过创建")
        return
    
    cursor.execute("""
        CREATE TABLE role_permissions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            role VARCHAR(50) NOT NULL,               -- 角色名称
            permission_id INTEGER NOT NULL,          -- 权限ID
            is_granted BOOLEAN DEFAULT TRUE,        -- 是否授予
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (permission_id) REFERENCES permissions(id),
            UNIQUE(role, permission_id)
        )
    """)
    
    print("✅ role_permissions 表创建成功")

def create_user_permissions_table(cursor):
    """创建用户权限关联表"""
    cursor.execute("""
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name='user_permissions'
    """)
    
    if cursor.fetchone():
        print("user_permissions 表已存在，跳过创建")
        return
    
    cursor.execute("""
        CREATE TABLE user_permissions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,               -- 用户ID
            permission_id INTEGER NOT NULL,         -- 权限ID
            is_granted BOOLEAN DEFAULT TRUE,       -- 是否授予
            granted_by INTEGER,                    -- 授权人ID
            granted_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            expires_at DATETIME,                   -- 过期时间
            FOREIGN KEY (user_id) REFERENCES users(id),
            FOREIGN KEY (permission_id) REFERENCES permissions(id),
            FOREIGN KEY (granted_by) REFERENCES users(id),
            UNIQUE(user_id, permission_id)
        )
    """)
    
    print("✅ user_permissions 表创建成功")

def insert_initial_permissions(cursor):
    """插入初始化权限数据"""
    
    # 定义权限数据
    permissions_data = [
        # 工单管理权限
        ('查看工单', 'work_order.view', 'work_order', '查看工单列表和详情'),
        ('创建工单', 'work_order.create', 'work_order', '创建新的工单'),
        ('编辑工单', 'work_order.edit', 'work_order', '编辑工单信息'),
        ('删除工单', 'work_order.delete', 'work_order', '删除工单'),
        ('分配工单', 'work_order.assign', 'work_order', '分配工单给其他用户'),
        ('处理工单', 'work_order.process', 'work_order', '处理和更新工单状态'),
        ('查看所有工单', 'work_order.view_all', 'work_order', '查看所有用户的工单'),
        ('导出工单', 'work_order.export', 'work_order', '导出工单数据'),
        
        # 材料管理权限
        ('查看材料', 'material.view', 'material', '查看材料列表和详情'),
        ('创建材料', 'material.create', 'material', '添加新材料'),
        ('编辑材料', 'material.edit', 'material', '编辑材料信息'),
        ('删除材料', 'material.delete', 'material', '删除材料'),
        ('材料入库', 'material.stock_in', 'material', '材料入库操作'),
        ('材料出库', 'material.stock_out', 'material', '材料出库操作'),
        ('查看库存报告', 'material.stock_report', 'material', '查看库存统计报告'),
        ('材料分类管理', 'material.category_manage', 'material', '管理材料分类'),
        
        # 用户管理权限
        ('查看用户', 'user.view', 'user', '查看用户列表和详情'),
        ('创建用户', 'user.create', 'user', '创建新用户'),
        ('编辑用户', 'user.edit', 'user', '编辑用户信息'),
        ('删除用户', 'user.delete', 'user', '删除用户'),
        ('重置密码', 'user.reset_password', 'user', '重置用户密码'),
        ('分配角色', 'user.assign_role', 'user', '分配用户角色'),
        ('管理权限', 'user.manage_permissions', 'user', '管理用户权限'),
        
        # 区域管理权限
        ('查看区域', 'area.view', 'area', '查看区域列表'),
        ('创建区域', 'area.create', 'area', '创建新区域'),
        ('编辑区域', 'area.edit', 'area', '编辑区域信息'),
        ('删除区域', 'area.delete', 'area', '删除区域'),
        
        # 组织管理权限
        ('查看组织', 'group.view', 'group', '查看组织架构'),
        ('创建组织', 'group.create', 'group', '创建新组织'),
        ('编辑组织', 'group.edit', 'group', '编辑组织信息'),
        ('删除组织', 'group.delete', 'group', '删除组织'),
        
        # 车辆管理权限
        ('查看车辆', 'vehicle.view', 'vehicle', '查看车辆列表'),
        ('创建车辆', 'vehicle.create', 'vehicle', '添加新车辆'),
        ('编辑车辆', 'vehicle.edit', 'vehicle', '编辑车辆信息'),
        ('删除车辆', 'vehicle.delete', 'vehicle', '删除车辆'),
        
        # 报告统计权限
        ('查看报告', 'report.view', 'report', '查看各类统计报告'),
        ('导出报告', 'report.export', 'report', '导出报告数据'),
        ('高级统计', 'report.advanced', 'report', '查看高级统计分析'),
        
        # 系统管理权限
        ('系统配置', 'system.config', 'system', '系统参数配置'),
        ('数据备份', 'system.backup', 'system', '数据备份操作'),
        ('日志查看', 'system.logs', 'system', '查看系统日志'),
        ('字典管理', 'system.dictionary', 'system', '管理系统字典'),
        ('模板管理', 'system.template', 'system', '管理表单模板')
    ]
    
    # 插入权限数据
    cursor.executemany("""
        INSERT INTO permissions (name, code, module, description)
        VALUES (?, ?, ?, ?)
    """, permissions_data)
    
    print(f"✅ 插入了 {len(permissions_data)} 个权限")
    
    # 获取权限ID映射
    cursor.execute("SELECT id, code FROM permissions")
    permission_map = {code: id for id, code in cursor.fetchall()}
    
    # 定义角色权限分配
    role_permissions_data = []
    
    # 管理员权限（拥有所有权限）
    admin_permissions = list(permission_map.values())
    for perm_id in admin_permissions:
        role_permissions_data.append(('admin', perm_id, True))
    
    # 经理权限
    manager_permission_codes = [
        # 工单管理
        'work_order.view', 'work_order.create', 'work_order.edit', 'work_order.assign', 
        'work_order.process', 'work_order.view_all', 'work_order.export',
        # 材料管理
        'material.view', 'material.create', 'material.edit', 'material.stock_in', 
        'material.stock_out', 'material.stock_report',
        # 用户管理（有限）
        'user.view', 'user.edit',
        # 区域管理
        'area.view', 'area.create', 'area.edit',
        # 组织管理
        'group.view',
        # 车辆管理
        'vehicle.view', 'vehicle.create', 'vehicle.edit',
        # 报告统计
        'report.view', 'report.export', 'report.advanced'
    ]
    
    for code in manager_permission_codes:
        if code in permission_map:
            role_permissions_data.append(('manager', permission_map[code], True))
    
    # 普通用户权限
    user_permission_codes = [
        # 工单管理（基础）
        'work_order.view', 'work_order.create', 'work_order.process',
        # 材料管理（查看）
        'material.view',
        # 区域管理（查看）
        'area.view',
        # 车辆管理（查看）
        'vehicle.view',
        # 报告统计（基础）
        'report.view'
    ]
    
    for code in user_permission_codes:
        if code in permission_map:
            role_permissions_data.append(('user', permission_map[code], True))
    
    # 插入角色权限数据
    cursor.executemany("""
        INSERT INTO role_permissions (role, permission_id, is_granted)
        VALUES (?, ?, ?)
    """, role_permissions_data)
    
    print(f"✅ 插入了 {len(role_permissions_data)} 个角色权限分配")
    print(f"   - 管理员: {len(admin_permissions)} 个权限")
    print(f"   - 经理: {len(manager_permission_codes)} 个权限")
    print(f"   - 用户: {len(user_permission_codes)} 个权限")

def main():
    """主函数"""
    print("开始创建用户权限配置表...")
    
    if create_permissions_tables():
        print("\n🎉 用户权限配置表创建成功！")
        print("\n已完成：")
        print("1. ✅ 创建permissions表")
        print("2. ✅ 创建role_permissions表")
        print("3. ✅ 创建user_permissions表")
        print("4. ✅ 插入权限和角色权限数据")
        print("\n下一步可以：")
        print("1. 创建权限管理API接口")
        print("2. 更新前端权限检查逻辑")
        print("3. 实现细粒度权限控制")
    else:
        print("\n❌ 用户权限配置表创建失败")

if __name__ == '__main__':
    main()