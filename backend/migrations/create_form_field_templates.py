#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建表单字段模板表迁移脚本
用于动态配置表单字段，替代前端硬编码的表单结构
"""

import sqlite3
import os
import json
from datetime import datetime

def get_db_path():
    """获取数据库路径"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    backend_dir = os.path.dirname(current_dir)
    db_path = os.path.join(backend_dir, 'instance', 'dispatch_system.db')
    return db_path

def create_form_field_templates_table():
    """创建表单字段模板表"""
    db_path = get_db_path()
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查表是否已存在
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='form_field_templates'
        """)
        
        if cursor.fetchone():
            print("form_field_templates 表已存在，跳过创建")
            return True
        
        # 创建表单字段模板表
        cursor.execute("""
            CREATE TABLE form_field_templates (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                form_type VARCHAR(50) NOT NULL,          -- 表单类型
                field_name VARCHAR(100) NOT NULL,        -- 字段名称
                field_label VARCHAR(100) NOT NULL,       -- 字段标签
                field_type VARCHAR(50) NOT NULL,         -- 字段类型
                field_options TEXT,                      -- 字段选项(JSON格式)
                validation_rules TEXT,                   -- 验证规则(JSON格式)
                default_value TEXT,                      -- 默认值
                placeholder VARCHAR(200),                -- 占位符
                help_text TEXT,                          -- 帮助文本
                sort_order INTEGER DEFAULT 0,           -- 排序
                is_required BOOLEAN DEFAULT FALSE,      -- 是否必填
                is_readonly BOOLEAN DEFAULT FALSE,      -- 是否只读
                is_visible BOOLEAN DEFAULT TRUE,        -- 是否可见
                is_active BOOLEAN DEFAULT TRUE,         -- 是否启用
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(form_type, field_name)
            )
        """)
        
        print("✅ form_field_templates 表创建成功")
        
        # 插入初始化数据
        insert_initial_templates(cursor)
        
        conn.commit()
        conn.close()
        
        print("✅ 表单字段模板表创建和初始化完成")
        return True
        
    except Exception as e:
        print(f"❌ 创建表单字段模板表失败: {str(e)}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

def insert_initial_templates(cursor):
    """插入初始化模板数据"""
    
    # 工单创建表单模板
    work_order_form_templates = [
        # 基本信息字段
        ('work_order_create', 'title', '工单标题', 'input', None, 
         json.dumps({'required': True, 'maxLength': 100}), '', '请输入工单标题', '工单的简要描述', 1, True, False, True, True),
        
        ('work_order_create', 'description', '详细描述', 'textarea', None,
         json.dumps({'required': True, 'maxLength': 500}), '', '请详细描述问题或需求', '详细说明工单内容', 2, True, False, True, True),
        
        ('work_order_create', 'type', '工单类型', 'select', 
         json.dumps([{'value': 'maintenance', 'label': '维修'}, {'value': 'cleaning', 'label': '清洁'}, {'value': 'safety', 'label': '安全检查'}, {'value': 'inspection', 'label': '设备巡检'}, {'value': 'other', 'label': '其他'}]),
         json.dumps({'required': True}), 'maintenance', '请选择工单类型', '选择合适的工单类型', 3, True, False, True, True),
        
        ('work_order_create', 'priority', '优先级', 'select',
         json.dumps([{'value': 'low', 'label': '低'}, {'value': 'medium', 'label': '中'}, {'value': 'high', 'label': '高'}, {'value': 'urgent', 'label': '紧急'}]),
         json.dumps({'required': True}), 'medium', '请选择优先级', '根据紧急程度选择优先级', 4, True, False, True, True),
        
        ('work_order_create', 'area_id', '所属区域', 'select',
         json.dumps([]), json.dumps({'required': True}), '', '请选择区域', '选择工单所属区域', 5, True, False, True, True),
        
        ('work_order_create', 'expected_completion_time', '期望完成时间', 'datetime',
         None, json.dumps({'required': False}), '', '请选择期望完成时间', '预期完成的时间', 6, False, False, True, True),
        
        # 故障信息字段
        ('work_order_create', 'failure_reason', '故障原因', 'select',
         json.dumps([{'value': 'equipment_failure', 'label': '设备故障'}, {'value': 'circuit_issue', 'label': '电路问题'}, {'value': 'software_issue', 'label': '软件故障'}, {'value': 'human_damage', 'label': '人为损坏'}, {'value': 'natural_wear', 'label': '自然损耗'}, {'value': 'other', 'label': '其他'}]),
         json.dumps({'required': False}), '', '请选择故障原因', '选择导致问题的主要原因', 7, False, False, True, True),
        
        ('work_order_create', 'attachments', '附件上传', 'file',
         json.dumps({'accept': 'image/*,.pdf,.doc,.docx', 'multiple': True}), json.dumps({'maxSize': '10MB', 'maxCount': 5}), '', '上传相关图片或文档', '支持图片、PDF、Word文档', 8, False, False, True, True)
    ]
    
    # 工单处理表单模板
    work_order_process_templates = [
        ('work_order_process', 'process_description', '处理描述', 'textarea',
         None, json.dumps({'required': True, 'maxLength': 500}), '', '请描述处理过程', '详细记录处理步骤和结果', 1, True, False, True, True),
        
        ('work_order_process', 'materials_used', '使用材料', 'material_selector',
         None, json.dumps({'required': False}), '', '选择使用的材料', '记录本次处理使用的材料', 2, False, False, True, True),
        
        ('work_order_process', 'time_spent', '耗时(小时)', 'number',
         json.dumps({'min': 0, 'max': 24, 'step': 0.5}), json.dumps({'required': True}), '', '请输入耗时', '记录实际处理时间', 3, True, False, True, True),
        
        ('work_order_process', 'status', '处理状态', 'select',
         json.dumps([{'value': 'in_progress', 'label': '处理中'}, {'value': 'completed', 'label': '已完成'}, {'value': 'cancelled', 'label': '已取消'}]),
         json.dumps({'required': True}), 'in_progress', '请选择状态', '更新工单处理状态', 4, True, False, True, True),
        
        ('work_order_process', 'completion_photos', '完成照片', 'file',
         json.dumps({'accept': 'image/*', 'multiple': True}), json.dumps({'maxSize': '5MB', 'maxCount': 3}), '', '上传完成后的照片', '记录处理完成后的现场情况', 5, False, False, True, True)
    ]
    
    # 材料管理表单模板
    material_form_templates = [
        ('material_create', 'name', '材料名称', 'input',
         None, json.dumps({'required': True, 'maxLength': 100}), '', '请输入材料名称', '材料的名称或型号', 1, True, False, True, True),
        
        ('material_create', 'category_id', '材料分类', 'select',
         json.dumps([]), json.dumps({'required': True}), '', '请选择分类', '选择材料所属分类', 2, True, False, True, True),
        
        ('material_create', 'specification', '规格型号', 'input',
         None, json.dumps({'required': False, 'maxLength': 100}), '', '请输入规格型号', '材料的详细规格', 3, False, False, True, True),
        
        ('material_create', 'unit', '计量单位', 'select',
         json.dumps([{'value': '个', 'label': '个'}, {'value': '套', 'label': '套'}, {'value': '米', 'label': '米'}, {'value': '公斤', 'label': '公斤'}, {'value': '升', 'label': '升'}, {'value': '包', 'label': '包'}]),
         json.dumps({'required': True}), '个', '请选择单位', '材料的计量单位', 4, True, False, True, True),
        
        ('material_create', 'current_stock', '当前库存', 'number',
         json.dumps({'min': 0}), json.dumps({'required': True}), '0', '请输入库存数量', '当前的库存数量', 5, True, False, True, True),
        
        ('material_create', 'min_stock', '最低库存', 'number',
         json.dumps({'min': 0}), json.dumps({'required': True}), '0', '请输入最低库存', '库存预警阈值', 6, True, False, True, True),
        
        ('material_create', 'price', '单价', 'number',
         json.dumps({'min': 0, 'step': 0.01}), json.dumps({'required': False}), '', '请输入单价', '材料的采购单价', 7, False, False, True, True),
        
        ('material_create', 'supplier', '供应商', 'input',
         None, json.dumps({'required': False, 'maxLength': 100}), '', '请输入供应商', '材料的主要供应商', 8, False, False, True, True)
    ]
    
    # 用户管理表单模板
    user_form_templates = [
        ('user_create', 'username', '用户名', 'input',
         None, json.dumps({'required': True, 'maxLength': 50, 'pattern': '^[a-zA-Z0-9_]+$'}), '', '请输入用户名', '只能包含字母、数字和下划线', 1, True, False, True, True),
        
        ('user_create', 'real_name', '真实姓名', 'input',
         None, json.dumps({'required': True, 'maxLength': 50}), '', '请输入真实姓名', '用户的真实姓名', 2, True, False, True, True),
        
        ('user_create', 'email', '邮箱', 'email',
         None, json.dumps({'required': True}), '', '请输入邮箱地址', '用于接收通知和找回密码', 3, True, False, True, True),
        
        ('user_create', 'phone', '手机号', 'input',
         None, json.dumps({'required': True, 'pattern': '^1[3-9]\\d{9}$'}), '', '请输入手机号', '11位手机号码', 4, True, False, True, True),
        
        ('user_create', 'role', '用户角色', 'select',
         json.dumps([{'value': 'admin', 'label': '管理员'}, {'value': 'manager', 'label': '经理'}, {'value': 'user', 'label': '用户'}]),
         json.dumps({'required': True}), 'user', '请选择角色', '用户在系统中的角色', 5, True, False, True, True),
        
        ('user_create', 'gender', '性别', 'radio',
         json.dumps([{'value': 'male', 'label': '男'}, {'value': 'female', 'label': '女'}]),
         json.dumps({'required': False}), '', '请选择性别', '用户性别', 6, False, False, True, True),
        
        ('user_create', 'areas', '管辖区域', 'multi_select',
         json.dumps([]), json.dumps({'required': False}), '', '请选择管辖区域', '用户负责的区域', 7, False, False, True, True)
    ]
    
    # 合并所有模板数据
    all_templates = (
        work_order_form_templates + work_order_process_templates + 
        material_form_templates + user_form_templates
    )
    
    # 批量插入数据
    cursor.executemany("""
        INSERT INTO form_field_templates 
        (form_type, field_name, field_label, field_type, field_options, validation_rules, 
         default_value, placeholder, help_text, sort_order, is_required, is_readonly, is_visible, is_active)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    """, all_templates)
    
    print(f"✅ 插入了 {len(all_templates)} 个表单字段模板")
    print(f"   - 工单创建表单: {len(work_order_form_templates)} 个字段")
    print(f"   - 工单处理表单: {len(work_order_process_templates)} 个字段")
    print(f"   - 材料管理表单: {len(material_form_templates)} 个字段")
    print(f"   - 用户管理表单: {len(user_form_templates)} 个字段")

def main():
    """主函数"""
    print("开始创建表单字段模板表...")
    
    if create_form_field_templates_table():
        print("\n🎉 表单字段模板表创建成功！")
        print("\n已完成：")
        print("1. ✅ 创建form_field_templates表")
        print("2. ✅ 插入各类表单模板数据")
        print("3. ✅ 支持动态表单配置")
        print("\n下一步可以：")
        print("1. 创建表单模板管理API接口")
        print("2. 创建动态表单组件")
        print("3. 替换前端硬编码的表单结构")
    else:
        print("\n❌ 表单字段模板表创建失败")

if __name__ == '__main__':
    main()