from . import db
from datetime import datetime

class ProcessRecord(db.Model):
    """工单处理记录"""
    __tablename__ = 'process_records'
    
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    work_order_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON>('work_orders.id'), nullable=False)
    action = db.Column(db.String(100), nullable=False)  # 操作类型：创建、分配、开始处理、完成等
    description = db.Column(db.Text, nullable=False)  # 处理描述
    operator_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)  # 操作人
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 关联关系
    work_order = db.relationship('WorkOrder', backref='process_records')
    operator = db.relationship('User', backref='process_records')
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'work_order_id': self.work_order_id,
            'action': self.action,
            'description': self.description,
            'operator_id': self.operator_id,
            'operator': self.operator.name if self.operator else None,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    def __repr__(self):
        return f'<ProcessRecord {self.id}: {self.action}>'