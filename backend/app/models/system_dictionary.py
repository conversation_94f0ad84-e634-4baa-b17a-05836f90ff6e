from . import db
from datetime import datetime

class SystemDictionary(db.Model):
    __tablename__ = 'system_dictionaries'
    
    id = db.Column(db.Integer, primary_key=True)
    dict_type = db.Column(db.String(50), nullable=False)  # 字典类型
    dict_key = db.Column(db.String(50), nullable=False)   # 字典键值
    dict_label = db.Column(db.String(100), nullable=False)  # 显示标签
    dict_value = db.Column(db.String(100), nullable=True)   # 字典值
    sort_order = db.Column(db.Integer, default=0)  # 排序
    is_active = db.Column(db.<PERSON><PERSON><PERSON>, default=True)  # 是否启用
    is_system = db.Column(db.Boolean, default=False)  # 是否系统内置
    parent_id = db.Column(db.Integer, db.<PERSON>ey('system_dictionaries.id'), nullable=True)  # 父级ID
    description = db.Column(db.Text, nullable=True)  # 描述
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 自关联关系
    children = db.relationship('SystemDictionary', backref=db.backref('parent', remote_side=[id]))
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'dict_type': self.dict_type,
            'dict_key': self.dict_key,
            'dict_label': self.dict_label,
            'dict_value': self.dict_value,
            'sort_order': self.sort_order,
            'is_active': self.is_active,
            'is_system': self.is_system,
            'parent_id': self.parent_id,
            'description': self.description,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'children': [child.to_dict() for child in self.children] if self.children else []
        }
    
    def __repr__(self):
        return f'<SystemDictionary {self.dict_type}:{self.dict_key}={self.dict_label}>'