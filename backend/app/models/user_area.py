from . import db

class UserArea(db.Model):
    """用户区域关联表（多对多关系）"""
    __tablename__ = 'user_areas'
    
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    user_id = db.<PERSON>umn(db.<PERSON><PERSON><PERSON>, db.<PERSON>('users.id'), nullable=False)
    area_id = db.<PERSON>umn(db.Integer, db.<PERSON>ey('areas.id'), nullable=False)
    
    # 创建复合唯一索引，防止重复关联
    __table_args__ = (db.UniqueConstraint('user_id', 'area_id', name='unique_user_area'),)
    
    def __repr__(self):
        return f'<UserArea user_id={self.user_id} area_id={self.area_id}>'