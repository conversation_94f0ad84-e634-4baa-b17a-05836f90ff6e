from flask import Blueprint
from .work_orders_core import work_orders_core_bp
from .work_orders_materials import work_orders_materials_bp
from .work_orders_fields import work_orders_fields_bp
from .work_orders_analytics import work_orders_analytics_bp
from .work_orders_process import work_orders_process_bp

# 创建主工单蓝图
work_orders_bp = Blueprint('work_orders', __name__)

# 注册所有子模块路由到主蓝图
def register_sub_blueprints():
    """注册所有子模块蓝图到主蓝图"""
    work_orders_bp.register_blueprint(work_orders_core_bp)
    work_orders_bp.register_blueprint(work_orders_materials_bp)
    work_orders_bp.register_blueprint(work_orders_fields_bp)
    work_orders_bp.register_blueprint(work_orders_analytics_bp, url_prefix='/analytics')
    work_orders_bp.register_blueprint(work_orders_process_bp)

# 立即注册子蓝图
register_sub_blueprints()

# 为了向后兼容，提供注册函数
def register_work_order_blueprints(app):
    """注册工单蓝图到应用（向后兼容）"""
    app.register_blueprint(work_orders_bp)