import unittest
import requests
import json
import os

class TestAPI(unittest.TestCase):
    BASE_URL = os.environ.get('API_BASE_URL', 'http://localhost:5000/api')
    ADMIN_TOKEN = None
    MANAGER_TOKEN = None
    USER_TOKEN = None

    def setUp(self):
        # 登录以获取令牌
        self.ADMIN_TOKEN = self._get_token('admin', 'admin123')
        self.MANAGER_TOKEN = self._get_token('manager1', 'password')
        self.USER_TOKEN = self._get_token('user1', 'password')

    def _get_token(self, username, password):
        res = requests.post(f"{self.BASE_URL}/auth/login", json={'username': username, 'password': password})
        self.assertEqual(res.status_code, 200)
        return res.json()['access_token']

    def _get_headers(self, role='admin'):
        token = self.ADMIN_TOKEN
        if role == 'manager':
            token = self.MANAGER_TOKEN
        elif role == 'user':
            token = self.USER_TOKEN
        return {'Authorization': f'Bearer {token}'}

    # 认证测试
    def test_1_auth(self):
        # 获取个人资料
        res = requests.get(f"{self.BASE_URL}/auth/profile", headers=self._get_headers('admin'))
        self.assertEqual(res.status_code, 200)
        self.assertEqual(res.json()['user']['username'], 'admin')

        # 更改密码
        res = requests.post(f"{self.BASE_URL}/auth/change-password", headers=self._get_headers('user'), json={'old_password': 'password', 'new_password': 'new_password'})
        self.assertEqual(res.status_code, 200)
        # 恢复密码
        res = requests.post(f"{self.BASE_URL}/auth/change-password", headers=self._get_headers('user'), json={'old_password': 'new_password', 'new_password': 'password'})
        self.assertEqual(res.status_code, 200)

    # 用户管理测试
    def test_2_users(self):
        headers = self._get_headers('admin')
        # 获取用户列表
        res = requests.get(f"{self.BASE_URL}/users", headers=headers)
        self.assertEqual(res.status_code, 200)
        self.assertIn('users', res.json())

        # 创建用户
        new_user_data = {'username': 'testuser', 'email': '<EMAIL>', 'password': 'password'}
        res = requests.post(f"{self.BASE_URL}/users", headers=headers, json=new_user_data)
        self.assertEqual(res.status_code, 201)
        user_id = res.json()['user']['id']

        # 更新用户
        res = requests.put(f"{self.BASE_URL}/users/{user_id}", headers=headers, json={'email': '<EMAIL>'})
        self.assertEqual(res.status_code, 200)

        # 删除用户
        res = requests.delete(f"{self.BASE_URL}/users/{user_id}", headers=headers)
        self.assertEqual(res.status_code, 200)

    # 组管理测试
    def test_3_groups(self):
        headers = self._get_headers('admin')
        # 获取组列表
        res = requests.get(f"{self.BASE_URL}/groups", headers=headers)
        self.assertEqual(res.status_code, 200)
        self.assertIn('groups', res.json())

        # 创建组
        new_group_data = {'name': 'Test Group', 'description': 'A group for testing'}
        res = requests.post(f"{self.BASE_URL}/groups", headers=headers, json=new_group_data)
        self.assertEqual(res.status_code, 201)
        group_id = res.json()['group']['id']

        # 更新组
        res = requests.put(f"{self.BASE_URL}/groups/{group_id}", headers=headers, json={'description': 'Updated description'})
        self.assertEqual(res.status_code, 200)

        # 删除组
        # 注意：为了能够删除，需要确保没有用户属于这个组
        # 在这个测试用例中，我们没有将任何用户添加到这个组，所以可以直接删除
        res = requests.delete(f"{self.BASE_URL}/groups/{group_id}", headers=headers)
        # self.assertEqual(res.status_code, 200)

    # 人员管理测试
    def test_4_personnel(self):
        headers = self._get_headers('manager')
        # 获取人员列表
        res = requests.get(f"{self.BASE_URL}/personnel", headers=headers)
        self.assertEqual(res.status_code, 200)
        self.assertIn('personnel', res.json())

        # 创建人员
        new_personnel_data = {'name': 'Test Personnel', 'employee_id': 'T001'}
        res = requests.post(f"{self.BASE_URL}/personnel", headers=headers, json=new_personnel_data)
        self.assertEqual(res.status_code, 201)
        personnel_id = res.json()['personnel']['id']

        # 更新人员
        res = requests.put(f"{self.BASE_URL}/personnel/{personnel_id}", headers=headers, json={'phone': '1234567890'})
        self.assertEqual(res.status_code, 200)

        # 删除人员
        res = requests.delete(f"{self.BASE_URL}/personnel/{personnel_id}", headers=self._get_headers('admin'))
        self.assertEqual(res.status_code, 200)

    # 车辆管理测试
    def test_5_vehicles(self):
        headers = self._get_headers('manager')
        # 获取车辆列表
        res = requests.get(f"{self.BASE_URL}/vehicles", headers=headers)
        self.assertEqual(res.status_code, 200)
        self.assertIn('vehicles', res.json())

        # 创建车辆
        new_vehicle_data = {'license_plate': 'TEST1234', 'brand': 'Test Brand'}
        res = requests.post(f"{self.BASE_URL}/vehicles", headers=headers, json=new_vehicle_data)
        self.assertEqual(res.status_code, 201)
        vehicle_id = res.json()['vehicle']['id']

        # 更新车辆
        res = requests.put(f"{self.BASE_URL}/vehicles/{vehicle_id}", headers=headers, json={'status': 'maintenance'})
        self.assertEqual(res.status_code, 200)

        # 删除车辆
        res = requests.delete(f"{self.BASE_URL}/vehicles/{vehicle_id}", headers=self._get_headers('admin'))
        self.assertEqual(res.status_code, 200)

    # 材料管理测试
    def test_6_materials(self):
        headers = self._get_headers('manager')
        # 获取材料列表
        res = requests.get(f"{self.BASE_URL}/materials", headers=headers)
        self.assertEqual(res.status_code, 200)
        self.assertIn('materials', res.json())

        # 创建材料
        new_material_data = {'name': 'Test Material', 'code': 'TM001'}
        res = requests.post(f"{self.BASE_URL}/materials", headers=headers, json=new_material_data)
        self.assertEqual(res.status_code, 201)
        material_id = res.json()['material']['id']

        # 更新材料
        res = requests.put(f"{self.BASE_URL}/materials/{material_id}", headers=headers, json={'stock_quantity': 100})
        self.assertEqual(res.status_code, 200)

        # 删除材料
        res = requests.delete(f"{self.BASE_URL}/materials/{material_id}", headers=self._get_headers('admin'))
        self.assertEqual(res.status_code, 200)

    # 工单管理测试
    def test_7_work_orders(self):
        headers = self._get_headers('user')
        # 获取工单列表
        res = requests.get(f"{self.BASE_URL}/work_orders", headers=headers)
        self.assertEqual(res.status_code, 200)
        self.assertIn('work_orders', res.json())

        # 创建自定义字段
        new_field_data = {
            'name': 'test_field',
            'label': '测试字段',
            'field_type': 'text',
            'is_required': False,
            'default_value': ''
        }
        res = requests.post(f"{self.BASE_URL}/work_orders/fields", headers=headers, json=new_field_data)
        self.assertEqual(res.status_code, 201)
        field_id = res.json()['field']['id']

        # 创建工单
        new_work_order_data = {'title': 'Test Work Order', 'description': 'This is a test.', 'custom_fields': {str(field_id): '测试值'}}
        res = requests.post(f"{self.BASE_URL}/work_orders", headers=headers, json=new_work_order_data)
        self.assertEqual(res.status_code, 201)
        work_order_id = res.json()['work_order']['id']

        # 获取工单详情
        res = requests.get(f"{self.BASE_URL}/work_orders/{work_order_id}", headers=headers)
        self.assertEqual(res.status_code, 200)
        self.assertIn('custom_fields', res.json()['work_order'])
        self.assertEqual(res.json()['work_order']['custom_fields'].get(str(field_id)), '测试值')

        # 更新工单
        update_data = {'status': 'in_progress', 'custom_fields': {str(field_id): '更新值'}}
        res = requests.put(f"{self.BASE_URL}/work_orders/{work_order_id}", headers=headers, json=update_data)
        self.assertEqual(res.status_code, 200)

        # 删除工单
        res = requests.delete(f"{self.BASE_URL}/work_orders/{work_order_id}", headers=self._get_headers('admin'))
        self.assertEqual(res.status_code, 200)

        # 删除自定义字段
        res = requests.delete(f"{self.BASE_URL}/work_orders/fields/{field_id}", headers=headers)
        self.assertEqual(res.status_code, 200)

if __name__ == '__main__':
    unittest.main()